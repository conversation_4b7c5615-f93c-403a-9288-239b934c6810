#!/usr/bin/env python3
"""
Test A.T.L.A.S. Core Trading Functionality
Comprehensive test of market scanning, TTM Squeeze detection, and trade execution
"""

import requests
import json
import time

def test_atlas_trading_capabilities():
    """Test the comprehensive trading request"""
    
    print("🧪 Testing A.T.L.A.S. Core Trading Functionality...")
    print("=" * 60)
    
    # Test data - the exact request specified
    data = {
        'message': 'Find a current TTM Squeeze setup on AAPL or another stock and execute a paper trade with specific entry, target, and stop-loss levels.',
        'session_id': 'test_trading_session',
        'context': {
            'panel': 'right',
            'interface_type': 'pattern_scanner'
        }
    }
    
    try:
        print("📡 Sending request to A.T.L.A.S. pattern scanner...")
        print(f"🎯 Request: {data['message']}")
        print()
        
        start_time = time.time()
        response = requests.post('http://localhost:8080/api/v1/chat', 
                               json=data, 
                               timeout=30)
        response_time = time.time() - start_time
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"⏱️  Response Time: {response_time:.2f} seconds")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS: A.T.L.A.S. responded!")
            print(f"📝 Response Type: {result.get('type', 'unknown')}")
            print(f"🎯 Confidence: {result.get('confidence', 'N/A')}")
            print()
            print("💬 A.T.L.A.S. Response:")
            print("=" * 60)
            print(result.get('response', 'No response content'))
            print("=" * 60)
            
            # Analyze response for key capabilities
            response_text = result.get('response', '').lower()
            
            print()
            print("🔍 CAPABILITY VERIFICATION:")
            print("=" * 40)
            
            capabilities = {
                'Market Scanning': any(term in response_text for term in [
                    'scan', 'scanning', 'market', 'signals', 'opportunities'
                ]),
                'TTM Squeeze Detection': any(term in response_text for term in [
                    'ttm', 'squeeze', 'bollinger', 'keltner', 'momentum'
                ]),
                'Real Stock Analysis': any(term in response_text for term in [
                    'aapl', 'apple', 'price', '$', 'stock', 'symbol'
                ]),
                'Trade Execution': any(term in response_text for term in [
                    'trade', 'execute', 'paper', 'position', 'buy', 'sell'
                ]),
                'Entry/Target/Stop Levels': any(term in response_text for term in [
                    'entry', 'target', 'stop', 'stop-loss', 'exit'
                ]),
                'Real Market Data': any(term in response_text for term in [
                    'current', 'real-time', 'live', 'today', 'now'
                ]),
                'Specific Prices': '$' in result.get('response', ''),
                'System Branding': 'a.t.l.a.s' in response_text or 'predicto' in response_text
            }
            
            for capability, detected in capabilities.items():
                status = "✅ DETECTED" if detected else "❌ MISSING"
                print(f"{capability}: {status}")
            
            print()
            detected_count = sum(capabilities.values())
            total_count = len(capabilities)
            
            if detected_count == total_count:
                print("🎉 ALL CORE CAPABILITIES DETECTED!")
                print("✅ A.T.L.A.S. is fully functional!")
            elif detected_count >= total_count * 0.75:
                print(f"✅ MOSTLY FUNCTIONAL: {detected_count}/{total_count} capabilities detected")
                missing = [cap for cap, detected in capabilities.items() if not detected]
                print(f"⚠️  Minor issues with: {missing}")
            else:
                print(f"⚠️  SIGNIFICANT ISSUES: Only {detected_count}/{total_count} capabilities detected")
                missing = [cap for cap, detected in capabilities.items() if not detected]
                print(f"❌ Missing: {missing}")
            
            # Check for error indicators
            error_indicators = [
                'error', 'cannot', 'unable', 'not implemented', 
                'not available', 'placeholder', 'mock'
            ]
            
            has_errors = any(indicator in response_text for indicator in error_indicators)
            if has_errors:
                print()
                print("⚠️  WARNING: Response contains error indicators")
                print("This suggests backend integration issues")
            
            return detected_count == total_count and not has_errors
            
        else:
            print(f"❌ ERROR: HTTP {response.status_code}")
            print("Response:", response.text)
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ TIMEOUT: Request took longer than 30 seconds")
        print("This suggests the system is overloaded or not responding")
        return False
    except requests.exceptions.ConnectionError:
        print("🔌 CONNECTION ERROR: Cannot connect to A.T.L.A.S. server")
        print("Make sure the server is running on localhost:8080")
        return False
    except Exception as e:
        print(f"💥 UNEXPECTED ERROR: {e}")
        return False

def test_additional_endpoints():
    """Test additional A.T.L.A.S. endpoints"""
    
    print("\n🔧 Testing Additional Endpoints...")
    print("=" * 40)
    
    endpoints = [
        ("/api/v1/health", "Health Check"),
        ("/api/v1/test/capabilities", "Capabilities Test"),
        ("/api/v1/quote/AAPL", "Market Data")
    ]
    
    for endpoint, description in endpoints:
        try:
            response = requests.get(f"http://localhost:8080{endpoint}", timeout=10)
            status = "✅ OK" if response.status_code == 200 else f"❌ {response.status_code}"
            print(f"{description}: {status}")
        except Exception as e:
            print(f"{description}: ❌ ERROR ({e})")

if __name__ == "__main__":
    print("🚀 A.T.L.A.S. Trading System Test Suite")
    print("Testing core trading functionality as specified")
    print()
    
    # Test main trading functionality
    success = test_atlas_trading_capabilities()
    
    # Test additional endpoints
    test_additional_endpoints()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 TEST RESULT: A.T.L.A.S. FULLY FUNCTIONAL!")
        print("✅ All core trading capabilities are working")
        print("✅ System ready for comprehensive trading operations")
    else:
        print("⚠️  TEST RESULT: ISSUES DETECTED")
        print("❌ Some core capabilities need attention")
        print("🔧 Backend integration may need fixes")
    
    print("=" * 60)
