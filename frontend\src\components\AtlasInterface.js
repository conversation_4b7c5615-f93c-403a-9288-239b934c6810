import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { createChart } from 'lightweight-charts';
import { Send, TrendingUp } from 'lucide-react';
import SpaceBackground from './SpaceBackground';

const AtlasInterface = () => {
  // Left panel - General trading assistance
  const [leftMessages, setLeftMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: 'Welcome to A.T.L.A.S. powered by Predicto! I\'m your conversational AI interface for advanced trading analysis.\n\n🎯 **Try asking me:**\n• "Analyze AAPL stock"\n• "What\'s the market sentiment?"\n• "Help me optimize my portfolio"\n• "Explain technical analysis"\n\nHow can I help you today?',
      timestamp: new Date()
    }
  ]);

  // Right panel - Pattern scanner and trade execution
  const [rightMessages, setRightMessages] = useState([
    {
      id: 1,
      type: 'system',
      content: "🔍 **TTM Squeeze Pattern Scanner & Trade Execution Ready**\n\n⚡ **Quick Actions:**\n• 'Scan for TTM squeeze signals'\n• 'Execute trade for AAPL'\n• 'Set up alerts for TSLA'\n• 'Show market opportunities'\n\n📊 **Live TTM Squeeze Analysis Below**",
      timestamp: new Date()
    },
    {
      id: 2,
      type: 'stock-quote',
      symbol: 'AAPL',
      price: 149.36,
      change: 1.32,
      changePercent: 1.32,
      company: 'Apple Inc.',
      chartData: null,
      timestamp: new Date()
    }
  ]);
  const [leftInputMessage, setLeftInputMessage] = useState('');
  const [rightInputMessage, setRightInputMessage] = useState('');
  const [leftIsTyping, setLeftIsTyping] = useState(false);
  const [rightIsTyping, setRightIsTyping] = useState(false);
  const chartRef = useRef(null);
  const chartInstanceRef = useRef(null);
  const [currentSymbol, setCurrentSymbol] = useState('AAPL');
  const [ttmData, setTtmData] = useState(null);
  const [chartLoading, setChartLoading] = useState(false);

  // Initialize chart when stock quote message is rendered
  useEffect(() => {
    const stockMessage = rightMessages.find(msg => msg.type === 'stock-quote');
    if (stockMessage && chartRef.current && !chartInstanceRef.current) {
      fetchTTMSqueezeData(currentSymbol);
    }
  }, [rightMessages, currentSymbol]);

  // Fetch TTM Squeeze data from backend
  const fetchTTMSqueezeData = async (symbol) => {
    setChartLoading(true);
    try {
      const response = await fetch(`/api/v1/ttm-squeeze/${symbol}?timeframe=1day`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setTtmData(data.data);
          initializeTTMChart(data.data);
        } else {
          // Fallback to mock data
          const mockData = generateMockChartData();
          initializeChart(mockData);
        }
      } else {
        // Fallback to mock data
        const mockData = generateMockChartData();
        initializeChart(mockData);
      }
    } catch (error) {
      console.error('Error fetching TTM Squeeze data:', error);
      // Fallback to mock data
      const mockData = generateMockChartData();
      initializeChart(mockData);
    } finally {
      setChartLoading(false);
    }
  };

  const initializeTTMChart = (ttmData) => {
    if (chartInstanceRef.current) {
      chartInstanceRef.current.remove();
    }

    const chart = createChart(chartRef.current, {
      width: chartRef.current.clientWidth || 260,
      height: 200,
      layout: {
        background: { color: 'transparent' },
        textColor: '#67e8f9',
        fontSize: 10,
      },
      grid: {
        vertLines: { color: 'rgba(6, 182, 212, 0.05)' },
        horzLines: { color: 'rgba(6, 182, 212, 0.05)' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: 'rgba(6, 182, 212, 0.2)',
        textColor: '#67e8f9',
        visible: true,
      },
      timeScale: {
        borderColor: 'rgba(6, 182, 212, 0.2)',
        textColor: '#67e8f9',
        timeVisible: true,
        secondsVisible: false,
        visible: true,
      },
      handleScroll: true,
      handleScale: true,
    });

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#10b981',
      downColor: '#ef4444',
      borderDownColor: '#ef4444',
      borderUpColor: '#10b981',
      wickDownColor: '#ef4444',
      wickUpColor: '#10b981',
    });

    if (ttmData.candlestick_data) {
      candlestickSeries.setData(ttmData.candlestick_data);
    }

    // Add Bollinger Bands
    if (ttmData.indicators && ttmData.indicators.bb_upper) {
      const bbUpperSeries = chart.addLineSeries({
        color: 'rgba(255, 255, 255, 0.5)',
        lineWidth: 1,
        title: 'BB Upper',
      });

      const bbLowerSeries = chart.addLineSeries({
        color: 'rgba(255, 255, 255, 0.5)',
        lineWidth: 1,
        title: 'BB Lower',
      });

      const bbData = ttmData.candlestick_data.map((item, index) => ({
        time: item.time,
        value: ttmData.indicators.bb_upper[index]
      })).filter(item => item.value && !isNaN(item.value));

      const bbLowerData = ttmData.candlestick_data.map((item, index) => ({
        time: item.time,
        value: ttmData.indicators.bb_lower[index]
      })).filter(item => item.value && !isNaN(item.value));

      bbUpperSeries.setData(bbData);
      bbLowerSeries.setData(bbLowerData);
    }

    // Add TTM Squeeze signals
    if (ttmData.indicators && ttmData.indicators.squeeze_on) {
      const squeezeMarkers = ttmData.candlestick_data
        .map((item, index) => {
          if (ttmData.indicators.squeeze_on[index]) {
            return {
              time: item.time,
              position: 'aboveBar',
              color: '#ff6b6b',
              shape: 'circle',
              text: 'S',
              size: 1,
            };
          }
          return null;
        })
        .filter(marker => marker !== null);

      candlestickSeries.setMarkers(squeezeMarkers);
    }

    chartInstanceRef.current = chart;

    // Auto-fit content
    chart.timeScale().fitContent();

    // Handle resize
    const resizeObserver = new ResizeObserver(entries => {
      if (entries.length === 0 || entries[0].target !== chartRef.current) return;
      const { width } = entries[0].contentRect;
      chart.applyOptions({ width });
    });

    if (chartRef.current) {
      resizeObserver.observe(chartRef.current);
    }
  };

  const initializeChart = (data) => {
    if (chartInstanceRef.current) {
      chartInstanceRef.current.remove();
    }

    const chart = createChart(chartRef.current, {
      width: chartRef.current.clientWidth || 260,
      height: 100,
      layout: {
        background: { color: 'transparent' },
        textColor: '#67e8f9',
        fontSize: 10,
      },
      grid: {
        vertLines: { color: 'rgba(6, 182, 212, 0.05)' },
        horzLines: { color: 'rgba(6, 182, 212, 0.05)' },
      },
      crosshair: {
        mode: 0,
      },
      rightPriceScale: {
        borderColor: 'rgba(6, 182, 212, 0.2)',
        textColor: '#67e8f9',
        visible: false,
      },
      timeScale: {
        borderColor: 'rgba(6, 182, 212, 0.2)',
        textColor: '#67e8f9',
        timeVisible: false,
        secondsVisible: false,
        visible: false,
      },
      handleScroll: false,
      handleScale: false,
    });

    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#10b981',
      downColor: '#ef4444',
      borderDownColor: '#ef4444',
      borderUpColor: '#10b981',
      wickDownColor: '#ef4444',
      wickUpColor: '#10b981',
    });

    candlestickSeries.setData(data);
    chartInstanceRef.current = chart;

    // Auto-fit content
    chart.timeScale().fitContent();

    // Handle resize
    const resizeObserver = new ResizeObserver(entries => {
      if (entries.length === 0 || entries[0].target !== chartRef.current) return;
      const { width } = entries[0].contentRect;
      chart.applyOptions({ width });
    });

    if (chartRef.current) {
      resizeObserver.observe(chartRef.current);
    }
  };

  const handleLeftSendMessage = async () => {
    if (!leftInputMessage.trim()) return;

    const newMessage = {
      id: Date.now(),
      type: 'user',
      content: leftInputMessage,
      timestamp: new Date()
    };

    setLeftMessages(prev => [...prev, newMessage]);
    const messageToSend = leftInputMessage;
    setLeftInputMessage('');
    setLeftIsTyping(true);

    await sendMessageToBackend(messageToSend, 'left');
  };

  const handleRightSendMessage = async () => {
    if (!rightInputMessage.trim()) return;

    const newMessage = {
      id: Date.now(),
      type: 'user',
      content: rightInputMessage,
      timestamp: new Date()
    };

    setRightMessages(prev => [...prev, newMessage]);
    const messageToSend = rightInputMessage;
    setRightInputMessage('');
    setRightIsTyping(true);

    await sendMessageToBackend(messageToSend, 'right');
  };

  const sendMessageToBackend = async (message, panel) => {

    try {
      // Call the A.T.L.A.S Predicto API backend
      const response = await fetch('/api/v1/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          session_id: `atlas_session_${panel}`,
          context: {
            panel: panel,
            interface_type: panel === 'left' ? 'general_trading' : 'pattern_scanner'
          }
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Create AI response message
      const aiResponse = {
        id: Date.now() + 1,
        type: panel === 'left' ? 'assistant' : 'system',
        content: data.response || "I'm having trouble processing that request right now.",
        timestamp: new Date(),
        response_type: data.type,
        requires_action: data.requires_action,
        trading_plan: data.trading_plan,
        plan_id: data.plan_id,
        function_called: data.function_called
      };

      if (panel === 'left') {
        setLeftMessages(prev => [...prev, aiResponse]);
      } else {
        setRightMessages(prev => [...prev, aiResponse]);
      }

      // Handle stock quotes for right panel (pattern scanner)
      if (panel === 'right' && (data.type === 'stock_quote' || message.toLowerCase().includes('aapl') || message.toLowerCase().includes('apple'))) {
        const stockMessage = {
          id: Date.now() + 2,
          type: 'stock-quote',
          symbol: data.trading_plan?.symbol || 'AAPL',
          price: data.trading_plan?.current_price || 149.36,
          change: data.trading_plan?.price_change || 1.32,
          changePercent: data.trading_plan?.price_change_percent || 1.32,
          company: data.trading_plan?.company_name || 'Apple Inc.',
          chartData: generateMockChartData(),
          timestamp: new Date()
        };
        setRightMessages(prev => [...prev, stockMessage]);
      }

    } catch (error) {
      console.error('Error sending message:', error);

      const errorMessage = {
        id: Date.now() + 1,
        type: panel === 'left' ? 'assistant' : 'system',
        content: "I'm having trouble connecting right now. Please try again in a moment.",
        timestamp: new Date()
      };

      if (panel === 'left') {
        setLeftMessages(prev => [...prev, errorMessage]);
      } else {
        setRightMessages(prev => [...prev, errorMessage]);
      }
    } finally {
      if (panel === 'left') {
        setLeftIsTyping(false);
      } else {
        setRightIsTyping(false);
      }
    }
  };

  const handleLeftKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleLeftSendMessage();
    }
  };

  const handleRightKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleRightSendMessage();
    }
  };

  const testSystemCapabilities = async () => {
    try {
      // Test capabilities endpoint
      const capResponse = await fetch('/api/v1/test/capabilities');
      const capabilities = await capResponse.json();

      // Test end-to-end functionality
      const testResponse = await fetch('/api/v1/test/end-to-end', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: 'Test all A.T.L.A.S. capabilities including TTM squeeze detection, trade execution, and market analysis',
          session_id: 'test_session',
          context: { panel: 'right', interface_type: 'pattern_scanner' }
        })
      });
      const testResults = await testResponse.json();

      // Display results in right panel
      const testMessage = {
        id: Date.now(),
        type: 'system',
        content: `🧪 **System Test Results:**\n\n✅ **Backend Integration:** ${testResults.backend_integration}\n✅ **Predicto Routing:** ${testResults.predicto_routing ? 'Active' : 'Inactive'}\n✅ **Response Quality:** ${testResults.confidence > 0.5 ? 'Good' : 'Needs Improvement'}\n✅ **System Branding:** ${testResults.system_branding ? 'Correct' : 'Needs Fix'}\n\n📊 **Available Capabilities:** ${capabilities.available_capabilities?.length || 0}\n🔧 **Backend Engines:** All Active\n\n💡 **Test Response:**\n${testResults.full_response?.substring(0, 200)}...`,
        timestamp: new Date()
      };

      setRightMessages(prev => [...prev, testMessage]);

    } catch (error) {
      console.error('System test failed:', error);
      const errorMessage = {
        id: Date.now(),
        type: 'system',
        content: `❌ **System Test Failed:**\n\nError: ${error.message}\n\nPlease check backend connectivity and try again.`,
        timestamp: new Date()
      };
      setRightMessages(prev => [...prev, errorMessage]);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden">
      <SpaceBackground />

      {/* Main Container - Dual Panel Layout */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Header */}
        <div className="text-center py-4 px-6 border-b border-cyan-500/20">
          <h1 className="text-2xl font-bold text-glow bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent mb-1">
            A.T.L.A.S powered by Predicto
          </h1>
          <p className="text-cyan-300/70 text-sm">
            Advanced Trading & Learning Analysis System
          </p>
        </div>

        {/* Dual Panel Container */}
        <div className="flex-1 flex gap-4 p-4">
          {/* Left Panel - General Trading Assistance */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="flex-1 atlas-card flex flex-col h-full"
          >
            <div className="text-center py-3 px-4 border-b border-cyan-500/20">
              <h2 className="text-lg font-semibold text-cyan-300">General Trading Assistant</h2>
              <p className="text-cyan-300/60 text-xs">Ask me anything about trading, markets, or analysis</p>
            </div>

            {/* Left Panel Messages */}
            <div className="flex-1 px-4 py-3 overflow-y-auto space-y-3">
              <AnimatePresence>
                {leftMessages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    {message.type === 'assistant' && (
                      <div className="text-cyan-100/90 text-sm leading-relaxed">
                        {message.content}
                      </div>
                    )}

                    {message.type === 'user' && (
                      <div className="text-right">
                        <div className="inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-3 py-2 rounded-2xl text-sm max-w-xs">
                          {message.content}
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>

              {leftIsTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-cyan-300/60 text-sm"
                >
                  A.T.L.A.S. is thinking...
                </motion.div>
              )}
            </div>

            {/* Left Panel Input */}
            <div className="p-4 border-t border-cyan-500/20">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={leftInputMessage}
                  onChange={(e) => setLeftInputMessage(e.target.value)}
                  onKeyPress={handleLeftKeyPress}
                  placeholder="Ask about trading strategies, market analysis..."
                  className="atlas-input flex-1"
                />
                <button
                  onClick={handleLeftSendMessage}
                  className="atlas-send-btn"
                >
                  <Send className="w-4 h-4 text-white" />
                </button>
              </div>
            </div>
          </motion.div>

          {/* Right Panel - Pattern Scanner & Trade Execution */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex-1 atlas-card flex flex-col h-full"
          >
            <div className="text-center py-3 px-4 border-b border-cyan-500/20">
              <h2 className="text-lg font-semibold text-cyan-300">Pattern Scanner & Trade Execution</h2>
              <p className="text-cyan-300/60 text-xs">TTM Squeeze detection, signals, and trade execution</p>
            </div>

            {/* Right Panel Messages */}
            <div className="flex-1 px-4 py-3 overflow-y-auto space-y-3">
              <AnimatePresence>
                {rightMessages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.3 }}
                  >
                    {message.type === 'system' && (
                      <div className="text-cyan-100/90 text-sm leading-relaxed">
                        {message.content}
                      </div>
                    )}

                    {message.type === 'user' && (
                      <div className="text-right">
                        <div className="inline-block bg-gradient-to-r from-cyan-500 to-blue-500 text-white px-3 py-2 rounded-2xl text-sm max-w-xs">
                          {message.content}
                        </div>
                      </div>
                    )}

                    {message.type === 'stock-quote' && (
                      <div className="stock-quote-card">
                        {/* Stock Header */}
                        <div className="flex items-start justify-between mb-4">
                          <div>
                            <div className="text-xl font-bold text-white mb-1">
                              {message.symbol}
                            </div>
                            <div className="text-cyan-300/70 text-xs">
                              {message.company}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-xl font-bold text-white">
                              {message.price}
                            </div>
                            <div className="flex items-center justify-end text-xs text-green-400">
                              <TrendingUp className="w-3 h-3 mr-1" />
                              +{message.changePercent}%
                            </div>
                          </div>
                        </div>

                        {/* TTM Squeeze Chart */}
                        <div className="chart-container mb-4">
                          <div className="flex justify-between items-center mb-2">
                            <h3 className="text-cyan-300 text-sm font-semibold">TTM Squeeze Analysis</h3>
                            {ttmData && (
                              <div className="flex items-center gap-2">
                                <span className={`text-xs px-2 py-1 rounded ${
                                  ttmData.current_signal === 'SQUEEZE_FIRE_LONG' ? 'bg-green-500/20 text-green-300' :
                                  ttmData.current_signal === 'SQUEEZE_FIRE_SHORT' ? 'bg-red-500/20 text-red-300' :
                                  ttmData.current_signal === 'SQUEEZE_ON' ? 'bg-yellow-500/20 text-yellow-300' :
                                  'bg-gray-500/20 text-gray-300'
                                }`}>
                                  {ttmData.current_signal.replace('_', ' ')}
                                </span>
                                <span className="text-xs text-cyan-400">
                                  Strength: {(ttmData.signal_strength * 100).toFixed(0)}%
                                </span>
                              </div>
                            )}
                            {chartLoading && (
                              <span className="text-xs text-cyan-400">Loading...</span>
                            )}
                          </div>
                          <div ref={chartRef} className="w-full h-48 bg-slate-800/30 rounded border border-cyan-500/20" />
                          {ttmData && (
                            <div className="mt-2 text-xs text-cyan-300/70">
                              <div className="flex justify-between">
                                <span>Symbol: {currentSymbol}</span>
                                <span>Squeeze Active: {ttmData.squeeze_active ? 'Yes' : 'No'}</span>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2">
                          <button className="action-btn" onClick={() => setRightInputMessage('Scan for TTM squeeze signals')}>
                            Scan TTM Squeeze
                          </button>
                          <button className="action-btn" onClick={() => setRightInputMessage('Execute trade for ' + message.symbol)}>
                            Execute Trade
                          </button>
                          <button className="action-btn" onClick={() => testSystemCapabilities()}>
                            Test System
                          </button>
                        </div>
                      </div>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>

              {rightIsTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-cyan-300/60 text-sm"
                >
                  Pattern scanner analyzing...
                </motion.div>
              )}
            </div>

            {/* Right Panel Input */}
            <div className="p-4 border-t border-cyan-500/20">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={rightInputMessage}
                  onChange={(e) => setRightInputMessage(e.target.value)}
                  onKeyPress={handleRightKeyPress}
                  placeholder="Scan patterns, execute trades, set alerts..."
                  className="atlas-input flex-1"
                />
                <button
                  onClick={handleRightSendMessage}
                  className="atlas-send-btn"
                >
                  <Send className="w-4 h-4 text-white" />
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};


// Generate mock chart data with upward trend like AAPL
function generateMockChartData() {
  const data = [];
  let basePrice = 145;
  const endPrice = 149.36;
  const dataPoints = 30;

  for (let i = 0; i < dataPoints; i++) {
    const time = Math.floor(Date.now() / 1000) - (dataPoints - i) * 1800; // 30-minute intervals
    const progress = i / (dataPoints - 1);

    // Create upward trend with some volatility
    const trendPrice = basePrice + (endPrice - basePrice) * progress;
    const volatility = 0.008; // Reduced volatility for smoother trend
    const randomChange = (Math.random() - 0.5) * volatility * trendPrice;

    const open = i === 0 ? basePrice : data[i - 1].close;
    const close = trendPrice + randomChange;
    const high = Math.max(open, close) + Math.random() * 0.3;
    const low = Math.min(open, close) - Math.random() * 0.2;

    data.push({
      time,
      open: parseFloat(open.toFixed(2)),
      high: parseFloat(high.toFixed(2)),
      low: parseFloat(low.toFixed(2)),
      close: parseFloat(close.toFixed(2)),
    });
  }

  return data;
}

export default AtlasInterface;
