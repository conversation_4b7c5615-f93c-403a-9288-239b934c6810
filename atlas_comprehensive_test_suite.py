#!/usr/bin/env python3
"""
A.T.L.A.S. Comprehensive Test Suite
Validates 25+ trading system capabilities and identifies gaps
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

class ATLASTestSuite:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.test_results = []
        self.failed_tests = []
        self.limitations_found = []
        
    def send_request(self, message: str, panel: str = "left", interface_type: str = "general_trading") -> Dict[str, Any]:
        """Send request to A.T.L.A.S. system"""
        data = {
            'message': message,
            'session_id': f'test_{int(time.time())}',
            'context': {
                'panel': panel,
                'interface_type': interface_type
            }
        }
        
        try:
            response = requests.post(f'{self.base_url}/api/v1/chat', json=data, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}", "response": response.text}
        except Exception as e:
            return {"error": str(e), "response": ""}
    
    def analyze_response(self, question: str, response: Dict[str, Any], expected_capabilities: List[str]) -> Dict[str, Any]:
        """Analyze response for limitations and capability gaps"""
        response_text = response.get('response', '').lower()
        
        # Check for limitation indicators
        limitation_phrases = [
            "i can't", "i don't have", "i'm not able", "i cannot",
            "as an ai", "i'm just a", "i don't actually",
            "i'm unable to", "not possible", "can't access",
            "don't have access", "not available", "cannot provide",
            "i'm a language model", "i don't have the ability"
        ]
        
        limitations = [phrase for phrase in limitation_phrases if phrase in response_text]
        
        # Check for expected capabilities
        capabilities_found = []
        for capability in expected_capabilities:
            if capability.lower() in response_text:
                capabilities_found.append(capability)
        
        # Check for A.T.L.A.S. branding
        has_atlas_branding = any(brand in response_text for brand in [
            'a.t.l.a.s', 'atlas', 'predicto'
        ])
        
        # Check for specific data (prices, percentages, etc.)
        has_specific_data = any(indicator in response_text for indicator in [
            '$', '%', 'entry:', 'target:', 'stop:', 'price:', 'shares'
        ])
        
        return {
            'question': question,
            'response': response.get('response', ''),
            'limitations': limitations,
            'capabilities_found': capabilities_found,
            'has_atlas_branding': has_atlas_branding,
            'has_specific_data': has_specific_data,
            'response_length': len(response.get('response', '')),
            'error': response.get('error')
        }
    
    def run_ttm_squeeze_tests(self):
        """Test TTM Squeeze Detection capabilities"""
        print("🎯 Testing TTM Squeeze Detection...")
        
        tests = [
            {
                'question': 'Scan AAPL for TTM Squeeze signals and provide specific entry, target, and stop-loss levels',
                'panel': 'right',
                'interface': 'pattern_scanner',
                'expected': ['ttm squeeze', 'entry', 'target', 'stop', 'aapl', '$']
            },
            {
                'question': 'Find the strongest TTM Squeeze setup among TSLA, NVDA, and MSFT with signal strength ratings',
                'panel': 'right', 
                'interface': 'pattern_scanner',
                'expected': ['signal strength', 'tsla', 'nvda', 'msft', 'strongest']
            },
            {
                'question': 'Analyze NVDA TTM Squeeze on multiple timeframes (daily, weekly) and confirm momentum alignment',
                'panel': 'right',
                'interface': 'pattern_scanner', 
                'expected': ['timeframes', 'daily', 'weekly', 'momentum', 'nvda']
            },
            {
                'question': 'Execute a TTM Squeeze scan on the entire market and rank top 5 opportunities by confidence score',
                'panel': 'right',
                'interface': 'pattern_scanner',
                'expected': ['market scan', 'top 5', 'confidence', 'ranking']
            },
            {
                'question': 'Validate TTM Squeeze signal on TSLA using the 4-criteria algorithm and provide accuracy assessment',
                'panel': 'right',
                'interface': 'pattern_scanner',
                'expected': ['4-criteria', 'algorithm', 'accuracy', 'validation', 'tsla']
            }
        ]
        
        for test in tests:
            print(f"  Testing: {test['question'][:60]}...")
            response = self.send_request(test['question'], test['panel'], test['interface'])
            analysis = self.analyze_response(test['question'], response, test['expected'])
            self.test_results.append(analysis)
            
            if analysis['limitations'] or not analysis['has_specific_data']:
                self.failed_tests.append(analysis)
                print(f"    ❌ FAILED - Limitations: {analysis['limitations']}")
            else:
                print(f"    ✅ PASSED")
    
    def run_trade_execution_tests(self):
        """Test Trade Execution capabilities"""
        print("💼 Testing Trade Execution...")
        
        tests = [
            {
                'question': 'Execute a paper trade for AAPL: Buy 100 shares with 2% stop-loss and 3% target',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['paper trade', 'aapl', '100 shares', '2%', '3%', 'executed']
            },
            {
                'question': 'Place a position-sized trade for TSLA based on portfolio risk of 1% with specific entry price',
                'panel': 'left',
                'interface': 'general_trading', 
                'expected': ['position sizing', 'tsla', '1%', 'portfolio risk', 'entry price']
            },
            {
                'question': 'Execute bracket order for NVDA with entry at $485, target $505, stop $470',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['bracket order', 'nvda', '$485', '$505', '$470']
            },
            {
                'question': 'Calculate optimal position size for MSFT trade with $500 risk tolerance',
                'panel': 'left', 
                'interface': 'general_trading',
                'expected': ['position size', 'msft', '$500', 'risk tolerance', 'optimal']
            },
            {
                'question': 'Execute trailing stop order for existing AAPL position with 1.5% trail',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['trailing stop', 'aapl', '1.5%', 'trail', 'existing position']
            }
        ]
        
        for test in tests:
            print(f"  Testing: {test['question'][:60]}...")
            response = self.send_request(test['question'], test['panel'], test['interface'])
            analysis = self.analyze_response(test['question'], response, test['expected'])
            self.test_results.append(analysis)
            
            if analysis['limitations'] or not analysis['has_specific_data']:
                self.failed_tests.append(analysis)
                print(f"    ❌ FAILED - Limitations: {analysis['limitations']}")
            else:
                print(f"    ✅ PASSED")
    
    def run_market_analysis_tests(self):
        """Test Market Analysis capabilities"""
        print("📊 Testing Market Analysis...")
        
        tests = [
            {
                'question': 'Provide real-time analysis of AAPL including current price, volume, and technical indicators',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['real-time', 'aapl', 'current price', 'volume', 'technical indicators']
            },
            {
                'question': 'Analyze TSLA sentiment using DistilBERT model and provide bullish/bearish score',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['sentiment', 'distilbert', 'tsla', 'bullish', 'bearish', 'score']
            },
            {
                'question': 'Generate LSTM price prediction for NVDA for next 5 trading days with confidence intervals',
                'panel': 'left',
                'interface': 'general_trading', 
                'expected': ['lstm', 'prediction', 'nvda', '5 days', 'confidence intervals']
            },
            {
                'question': 'Scan market for momentum breakouts and rank by strength with specific entry points',
                'panel': 'right',
                'interface': 'pattern_scanner',
                'expected': ['momentum breakouts', 'rank', 'strength', 'entry points']
            },
            {
                'question': 'Analyze MSFT using all technical indicators and provide comprehensive trading recommendation',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['technical indicators', 'msft', 'comprehensive', 'recommendation']
            }
        ]
        
        for test in tests:
            print(f"  Testing: {test['question'][:60]}...")
            response = self.send_request(test['question'], test['panel'], test['interface'])
            analysis = self.analyze_response(test['question'], response, test['expected'])
            self.test_results.append(analysis)
            
            if analysis['limitations'] or not analysis['has_specific_data']:
                self.failed_tests.append(analysis)
                print(f"    ❌ FAILED - Limitations: {analysis['limitations']}")
            else:
                print(f"    ✅ PASSED")

    def run_portfolio_management_tests(self):
        """Test Portfolio Management capabilities"""
        print("📈 Testing Portfolio Management...")

        tests = [
            {
                'question': 'Show current portfolio positions with P&L, allocation percentages, and risk metrics',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['portfolio', 'positions', 'p&l', 'allocation', 'risk metrics']
            },
            {
                'question': 'Calculate portfolio VaR (Value at Risk) and correlation analysis for current holdings',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['var', 'value at risk', 'correlation', 'analysis', 'holdings']
            },
            {
                'question': 'Optimize portfolio allocation using modern portfolio theory for maximum Sharpe ratio',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['optimize', 'allocation', 'modern portfolio theory', 'sharpe ratio']
            }
        ]

        for test in tests:
            print(f"  Testing: {test['question'][:60]}...")
            response = self.send_request(test['question'], test['panel'], test['interface'])
            analysis = self.analyze_response(test['question'], response, test['expected'])
            self.test_results.append(analysis)

            if analysis['limitations'] or not analysis['has_specific_data']:
                self.failed_tests.append(analysis)
                print(f"    ❌ FAILED - Limitations: {analysis['limitations']}")
            else:
                print(f"    ✅ PASSED")

    def run_options_trading_tests(self):
        """Test Options Trading capabilities"""
        print("📋 Testing Options Trading...")

        tests = [
            {
                'question': 'Analyze AAPL options flow and identify unusual activity with specific strike prices and expiration',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['options flow', 'aapl', 'unusual activity', 'strike prices', 'expiration']
            },
            {
                'question': 'Calculate Greeks (Delta, Gamma, Theta, Vega) for TSLA $250 calls expiring next Friday',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['greeks', 'delta', 'gamma', 'theta', 'vega', 'tsla', '$250']
            },
            {
                'question': 'Recommend optimal options strategy for NVDA based on current volatility and price action',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['options strategy', 'nvda', 'volatility', 'price action', 'recommend']
            }
        ]

        for test in tests:
            print(f"  Testing: {test['question'][:60]}...")
            response = self.send_request(test['question'], test['panel'], test['interface'])
            analysis = self.analyze_response(test['question'], response, test['expected'])
            self.test_results.append(analysis)

            if analysis['limitations'] or not analysis['has_specific_data']:
                self.failed_tests.append(analysis)
                print(f"    ❌ FAILED - Limitations: {analysis['limitations']}")
            else:
                print(f"    ✅ PASSED")

    def run_educational_tests(self):
        """Test Educational Features"""
        print("📚 Testing Educational Features...")

        tests = [
            {
                'question': 'Access trading book content about TTM Squeeze patterns and provide specific page references',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['trading book', 'ttm squeeze', 'page references', 'content']
            },
            {
                'question': 'Explain risk management principles from integrated trading books with practical examples',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['risk management', 'principles', 'trading books', 'examples']
            }
        ]

        for test in tests:
            print(f"  Testing: {test['question'][:60]}...")
            response = self.send_request(test['question'], test['panel'], test['interface'])
            analysis = self.analyze_response(test['question'], response, test['expected'])
            self.test_results.append(analysis)

            if analysis['limitations'] or not analysis['has_specific_data']:
                self.failed_tests.append(analysis)
                print(f"    ❌ FAILED - Limitations: {analysis['limitations']}")
            else:
                print(f"    ✅ PASSED")

    def run_system_integration_tests(self):
        """Test System Integration capabilities"""
        print("🔧 Testing System Integration...")

        tests = [
            {
                'question': 'Coordinate TTM Squeeze scan with sentiment analysis and ML predictions for AAPL comprehensive report',
                'panel': 'right',
                'interface': 'pattern_scanner',
                'expected': ['coordinate', 'ttm squeeze', 'sentiment', 'ml predictions', 'aapl', 'comprehensive']
            },
            {
                'question': 'Synchronize real-time data across all engines and provide system health status report',
                'panel': 'left',
                'interface': 'general_trading',
                'expected': ['synchronize', 'real-time data', 'engines', 'health status', 'system']
            }
        ]

        for test in tests:
            print(f"  Testing: {test['question'][:60]}...")
            response = self.send_request(test['question'], test['panel'], test['interface'])
            analysis = self.analyze_response(test['question'], response, test['expected'])
            self.test_results.append(analysis)

            if analysis['limitations'] or not analysis['has_specific_data']:
                self.failed_tests.append(analysis)
                print(f"    ❌ FAILED - Limitations: {analysis['limitations']}")
            else:
                print(f"    ✅ PASSED")

    def run_all_tests(self):
        """Run all test categories"""
        print("🚀 Starting A.T.L.A.S. Comprehensive Test Suite")
        print("=" * 60)

        start_time = datetime.now()

        # Run all test categories
        self.run_ttm_squeeze_tests()
        self.run_trade_execution_tests()
        self.run_market_analysis_tests()
        self.run_portfolio_management_tests()
        self.run_options_trading_tests()
        self.run_educational_tests()
        self.run_system_integration_tests()

        end_time = datetime.now()
        duration = end_time - start_time

        # Generate comprehensive report
        self.generate_report(duration)

    def generate_report(self, duration):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 A.T.L.A.S. TEST RESULTS SUMMARY")
        print("=" * 60)

        total_tests = len(self.test_results)
        failed_tests = len(self.failed_tests)
        passed_tests = total_tests - failed_tests

        print(f"⏱️  Test Duration: {duration}")
        print(f"📈 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if self.failed_tests:
            print("\n🔍 DETAILED FAILURE ANALYSIS:")
            print("-" * 40)

            for i, failure in enumerate(self.failed_tests, 1):
                print(f"\n{i}. FAILED TEST:")
                print(f"   Question: {failure['question'][:80]}...")
                print(f"   Limitations Found: {failure['limitations']}")
                print(f"   Has Specific Data: {failure['has_specific_data']}")
                print(f"   Has A.T.L.A.S. Branding: {failure['has_atlas_branding']}")
                print(f"   Response Length: {failure['response_length']} chars")

                if failure['error']:
                    print(f"   Error: {failure['error']}")

                # Show first 200 chars of response for analysis
                response_preview = failure['response'][:200] + "..." if len(failure['response']) > 200 else failure['response']
                print(f"   Response Preview: {response_preview}")

        # Identify common limitation patterns
        all_limitations = []
        for failure in self.failed_tests:
            all_limitations.extend(failure['limitations'])

        if all_limitations:
            from collections import Counter
            limitation_counts = Counter(all_limitations)

            print("\n🎯 MOST COMMON LIMITATIONS:")
            print("-" * 30)
            for limitation, count in limitation_counts.most_common(5):
                print(f"   '{limitation}': {count} occurrences")

        # Generate fix recommendations
        self.generate_fix_recommendations()

    def generate_fix_recommendations(self):
        """Generate specific fix recommendations"""
        print("\n🔧 RECOMMENDED FIXES:")
        print("-" * 25)

        if self.failed_tests:
            print("1. Update system prompts to eliminate generic AI disclaimers")
            print("2. Ensure all responses include specific trading data (prices, percentages)")
            print("3. Verify A.T.L.A.S. branding appears in all responses")
            print("4. Implement confident trading system responses")
            print("5. Add real market data integration for specific examples")
            print("6. Enhance response formatting for Predicto standards")
        else:
            print("🎉 All tests passed! A.T.L.A.S. is fully functional!")

def main():
    """Main execution function"""
    test_suite = ATLASTestSuite()
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()
