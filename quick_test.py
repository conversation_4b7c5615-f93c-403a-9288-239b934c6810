#!/usr/bin/env python3
"""
Quick test of A.T.L.A.S. system
"""

import requests
import json

def test_atlas():
    """Quick test of A.T.L.A.S. system"""
    
    print("🎯 A.T.L.A.S. QUICK TEST")
    print("=" * 30)
    
    # Simple greeting test
    data = {
        'message': 'Hello A.T.L.A.S., show me your trading capabilities',
        'session_id': 'quick_test'
    }
    
    try:
        print("📡 Testing A.T.L.A.S. response...")
        
        response = requests.post('http://localhost:8080/api/v1/chat', 
                               json=data, 
                               timeout=15)
        
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print()
            print("💬 A.T.L.A.S. Response:")
            print("-" * 40)
            print(result.get('response', 'No response'))
            print("-" * 40)
            return True
        else:
            print(f"❌ ERROR: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"💥 ERROR: {e}")
        return False

if __name__ == "__main__":
    success = test_atlas()
    
    if success:
        print("\n🎉 A.T.L.A.S. IS WORKING!")
    else:
        print("\n⚠️  System needs attention")
