#!/usr/bin/env python3
"""
Simple test to verify A.T.L.A.S. is responding
"""

import requests
import json

def test_atlas_response():
    """Test basic A.T.L.A.S. response"""
    
    print("🧪 Testing A.T.L.A.S. Basic Response...")
    
    # Simple test request
    data = {
        'message': 'Hello A.T.L.A.S., are you working?',
        'session_id': 'simple_test'
    }
    
    try:
        response = requests.post('http://localhost:8080/api/v1/chat', 
                               json=data, 
                               timeout=15)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"Response: {result.get('response', 'No response')[:200]}...")
            return True
        else:
            print(f"❌ Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_atlas_response()
