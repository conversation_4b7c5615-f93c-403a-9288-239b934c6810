#!/usr/bin/env python3
"""
A.T.L.A.S<PERSON>ginner-Focused Trading Test Suite
Tests goal-based trading requests, educational responsibility, and regulatory compliance
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Any

class ATLASBeginnerTestSuite:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url
        self.test_results = []
        self.failed_tests = []
        self.educational_failures = []
        self.compliance_failures = []
        
    def send_request(self, message: str, panel: str = "left") -> Dict[str, Any]:
        """Send request to A.T.L.A.S. system"""
        data = {
            'message': message,
            'session_id': f'beginner_test_{int(time.time())}',
            'context': {
                'panel': panel,
                'interface_type': 'general_trading'
            }
        }
        
        try:
            response = requests.post(f'{self.base_url}/api/v1/chat', json=data, timeout=30)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}", "response": response.text}
        except Exception as e:
            return {"error": str(e), "response": ""}
    
    def analyze_beginner_response(self, question: str, response: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze response for educational responsibility and compliance"""
        response_text = response.get('response', '').lower()
        
        # Educational responsibility indicators
        educational_indicators = [
            'risk management', 'education', 'learn', 'understand', 'paper trading',
            'practice', 'mentor', 'guidance', 'caution', 'realistic'
        ]
        
        # Compliance indicators
        compliance_indicators = [
            'disclaimer', 'risk', 'paper', 'educational', 'not financial advice',
            'past performance', 'no guarantee'
        ]
        
        # Strategy sophistication indicators
        strategy_indicators = [
            'ttm squeeze', 'hedging', 'scalping', 'options spread', 'pairs trading',
            'momentum breakout', 'risk management', 'position sizing'
        ]
        
        # Execution capability indicators
        execution_indicators = [
            'entry:', 'target:', 'stop:', 'execute', 'trade', 'buy', 'sell',
            'position size', 'bracket order'
        ]
        
        # Problematic profit promise indicators
        profit_promise_issues = [
            'guaranteed', 'sure profit', 'easy money', 'risk-free',
            'always profitable', 'can\'t lose'
        ]
        
        educational_score = sum(1 for indicator in educational_indicators if indicator in response_text)
        compliance_score = sum(1 for indicator in compliance_indicators if indicator in response_text)
        strategy_score = sum(1 for indicator in strategy_indicators if indicator in response_text)
        execution_score = sum(1 for indicator in execution_indicators if indicator in response_text)
        profit_issues = sum(1 for issue in profit_promise_issues if issue in response_text)
        
        # Check for A.T.L.A.S. branding (but not excessive)
        atlas_mentions = response_text.count('a.t.l.a.s') + response_text.count('atlas')
        appropriate_branding = 1 <= atlas_mentions <= 3
        
        # Check for specific trading data
        has_specific_data = any(indicator in response.get('response', '') for indicator in [
            '$', '%', 'entry:', 'target:', 'stop:', 'shares'
        ])
        
        return {
            'question': question,
            'response': response.get('response', ''),
            'educational_score': educational_score,
            'compliance_score': compliance_score,
            'strategy_score': strategy_score,
            'execution_score': execution_score,
            'profit_issues': profit_issues,
            'appropriate_branding': appropriate_branding,
            'has_specific_data': has_specific_data,
            'response_length': len(response.get('response', '')),
            'error': response.get('error')
        }
    
    def run_goal_based_profit_tests(self):
        """Test goal-based profit requests with educational responsibility"""
        print("💰 Testing Goal-Based Profit Requests...")
        
        tests = [
            "I want to make $200 in the next 3 days—what trades should I consider?",
            "Can you find me a simple stock setup that could earn $50 today and place the trade?",
            "I'm looking to grow $1,000 to $1,200 within a week—what's the best strategy?",
            "What's the safest way to make $100 this month, and can you execute it for me?",
            "Help me turn $500 into $600 in two days—recommend and trade an options spread.",
            "What are the top 3 stocks to buy for a $150 profit target today, and please buy them?",
            "How can I make $250 by the end of the week with minimal risk?",
            "Find me a 1-week options play on AAPL to hit $75 profit, and place the order.",
            "I need $300 profit by tomorrow—scan for a high-confidence TTM Squeeze and trade it.",
            "What's the best way to turn $2,000 into $2,200 in five days?",
            "Give me a low-volatility pick to make $100 this afternoon and place a bracket order.",
            "How do I safely make $150 this week using hedging, and can you set it up?",
            "Recommend an options strategy to make $200 in three days and trade it automatically.",
            "I want to profit $500 in the next two weeks—what portfolio allocation should I use?",
            "Plan a $150 profit path over 4 days and place each trade as signals appear."
        ]
        
        for i, question in enumerate(tests, 1):
            print(f"  {i:2d}. Testing: {question[:60]}...")
            response = self.send_request(question)
            analysis = self.analyze_beginner_response(question, response)
            self.test_results.append(analysis)
            
            # Evaluate educational responsibility
            if analysis['educational_score'] < 2 or analysis['compliance_score'] < 1:
                self.educational_failures.append(analysis)
                print(f"      ❌ EDUCATIONAL FAILURE - Ed:{analysis['educational_score']} Comp:{analysis['compliance_score']}")
            elif analysis['profit_issues'] > 0:
                self.compliance_failures.append(analysis)
                print(f"      ⚠️  COMPLIANCE ISSUE - Profit promises detected")
            elif not analysis['has_specific_data']:
                self.failed_tests.append(analysis)
                print(f"      ❌ MISSING DATA - No specific trading parameters")
            else:
                print(f"      ✅ PASSED - Ed:{analysis['educational_score']} Exec:{analysis['execution_score']}")
    
    def run_advanced_strategy_tests(self):
        """Test advanced strategy implementation"""
        print("🎯 Testing Advanced Strategy Implementation...")
        
        tests = [
            "Implement your best TTM Squeeze algorithm setup on TSLA and execute the trade",
            "Use your most effective hedging strategy for a $1,000 position in NVDA",
            "Execute a scalping strategy to make $20 within the next hour on high-volume stocks",
            "Deploy your optimal options spread strategy for AAPL earnings week",
            "Implement a pairs trading strategy using MSFT and GOOGL",
            "Execute your best momentum breakout strategy on the strongest market signal",
            "Use your advanced risk management system for a $5,000 day trading session",
            "Deploy your proprietary pattern recognition for intraday profit opportunities"
        ]
        
        for i, question in enumerate(tests, 1):
            print(f"  {i}. Testing: {question[:60]}...")
            response = self.send_request(question)
            analysis = self.analyze_beginner_response(question, response)
            self.test_results.append(analysis)
            
            # Evaluate strategy sophistication
            if analysis['strategy_score'] < 2:
                self.failed_tests.append(analysis)
                print(f"      ❌ FAILED - Strategy score too low: {analysis['strategy_score']}")
            elif not analysis['has_specific_data']:
                self.failed_tests.append(analysis)
                print(f"      ❌ FAILED - Missing specific trading data")
            else:
                print(f"      ✅ PASSED - Strategy:{analysis['strategy_score']} Exec:{analysis['execution_score']}")
    
    def run_immediate_execution_tests(self):
        """Test immediate execution requests"""
        print("⚡ Testing Immediate Execution Requests...")
        
        tests = [
            "Find me a bullish spread on TSLA to earn $100 by Friday and execute it",
            "Help me make $120 by the close tomorrow—what's the simplest trade?",
            "I need $50 in profit by tomorrow morning—what's the easiest way?",
            "What stock could give me $75 profit in the next 24 hours? Buy it for me.",
            "Scan for 4-star setups that could earn $75 today and place one trade",
            "Can you turn $800 into $900 this week with a mix of stocks and options?",
            "Show me a $500→$600 crypto trade in BTC or ETH for today, and execute it"
        ]
        
        for i, question in enumerate(tests, 1):
            print(f"  {i}. Testing: {question[:60]}...")
            response = self.send_request(question)
            analysis = self.analyze_beginner_response(question, response)
            self.test_results.append(analysis)
            
            # Evaluate immediate execution capability
            if analysis['execution_score'] < 3:
                self.failed_tests.append(analysis)
                print(f"      ❌ FAILED - Execution score too low: {analysis['execution_score']}")
            elif analysis['educational_score'] < 1:
                self.educational_failures.append(analysis)
                print(f"      ❌ EDUCATIONAL FAILURE - No risk education")
            else:
                print(f"      ✅ PASSED - Exec:{analysis['execution_score']} Ed:{analysis['educational_score']}")
    
    def run_all_tests(self):
        """Run all beginner-focused test categories"""
        print("🚀 Starting A.T.L.A.S. Beginner Trading Test Suite")
        print("=" * 70)
        
        start_time = datetime.now()
        
        # Run all test categories
        self.run_goal_based_profit_tests()
        self.run_advanced_strategy_tests()
        self.run_immediate_execution_tests()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Generate comprehensive report
        self.generate_beginner_report(duration)

    def generate_beginner_report(self, duration):
        """Generate comprehensive beginner test report"""
        print("\n" + "=" * 70)
        print("📊 A.T.L.A.S. BEGINNER TRADING TEST RESULTS")
        print("=" * 70)

        total_tests = len(self.test_results)
        failed_tests = len(self.failed_tests)
        educational_failures = len(self.educational_failures)
        compliance_failures = len(self.compliance_failures)
        passed_tests = total_tests - failed_tests - educational_failures - compliance_failures

        print(f"⏱️  Test Duration: {duration}")
        print(f"📈 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Technical Failures: {failed_tests}")
        print(f"📚 Educational Failures: {educational_failures}")
        print(f"⚖️  Compliance Failures: {compliance_failures}")
        print(f"📊 Overall Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        # Educational responsibility analysis
        if self.test_results:
            avg_educational_score = sum(r['educational_score'] for r in self.test_results) / len(self.test_results)
            avg_compliance_score = sum(r['compliance_score'] for r in self.test_results) / len(self.test_results)
            avg_strategy_score = sum(r['strategy_score'] for r in self.test_results) / len(self.test_results)
            avg_execution_score = sum(r['execution_score'] for r in self.test_results) / len(self.test_results)

            print(f"\n📊 AVERAGE SCORES:")
            print(f"   Educational Responsibility: {avg_educational_score:.1f}/10")
            print(f"   Regulatory Compliance: {avg_compliance_score:.1f}/5")
            print(f"   Strategy Sophistication: {avg_strategy_score:.1f}/8")
            print(f"   Execution Capability: {avg_execution_score:.1f}/6")

        # Detailed failure analysis
        if self.educational_failures:
            print(f"\n🎓 EDUCATIONAL RESPONSIBILITY FAILURES:")
            print("-" * 50)
            for i, failure in enumerate(self.educational_failures[:3], 1):
                print(f"\n{i}. Question: {failure['question'][:60]}...")
                print(f"   Educational Score: {failure['educational_score']}/10")
                print(f"   Compliance Score: {failure['compliance_score']}/5")
                print(f"   Issue: Insufficient risk education or mentoring approach")

        if self.compliance_failures:
            print(f"\n⚖️  REGULATORY COMPLIANCE FAILURES:")
            print("-" * 40)
            for i, failure in enumerate(self.compliance_failures[:3], 1):
                print(f"\n{i}. Question: {failure['question'][:60]}...")
                print(f"   Profit Issues: {failure['profit_issues']} problematic promises")
                print(f"   Issue: Inappropriate profit guarantees or unrealistic expectations")

        if self.failed_tests:
            print(f"\n🔧 TECHNICAL FAILURES:")
            print("-" * 25)
            for i, failure in enumerate(self.failed_tests[:3], 1):
                print(f"\n{i}. Question: {failure['question'][:60]}...")
                print(f"   Strategy Score: {failure['strategy_score']}/8")
                print(f"   Execution Score: {failure['execution_score']}/6")
                print(f"   Has Specific Data: {failure['has_specific_data']}")

        # Generate specific recommendations
        self.generate_beginner_recommendations()

    def generate_beginner_recommendations(self):
        """Generate specific recommendations for beginner trading improvements"""
        print(f"\n🎯 BEGINNER TRADING IMPROVEMENTS:")
        print("-" * 40)

        if self.educational_failures:
            print("1. 📚 ENHANCE EDUCATIONAL MENTORING:")
            print("   • Add more risk management education to all responses")
            print("   • Include 'paper trading first' recommendations")
            print("   • Provide realistic expectation management")
            print("   • Add learning resources and practice suggestions")

        if self.compliance_failures:
            print("2. ⚖️  IMPROVE REGULATORY COMPLIANCE:")
            print("   • Add appropriate disclaimers to profit-focused requests")
            print("   • Avoid guaranteeing specific profit amounts")
            print("   • Include 'past performance' warnings")
            print("   • Emphasize educational nature of recommendations")

        if self.failed_tests:
            print("3. 🔧 ENHANCE EXECUTION CAPABILITIES:")
            print("   • Provide specific entry/exit points for all strategies")
            print("   • Include position sizing calculations")
            print("   • Add stop-loss and risk management parameters")
            print("   • Demonstrate advanced strategy knowledge")

        # Success indicators
        successful_tests = [r for r in self.test_results
                          if r not in self.failed_tests
                          and r not in self.educational_failures
                          and r not in self.compliance_failures]

        if successful_tests:
            print(f"\n🎉 SUCCESS PATTERNS IDENTIFIED:")
            print("-" * 35)
            avg_successful_ed = sum(r['educational_score'] for r in successful_tests) / len(successful_tests)
            avg_successful_exec = sum(r['execution_score'] for r in successful_tests) / len(successful_tests)

            print(f"   • Successful responses average {avg_successful_ed:.1f} educational points")
            print(f"   • Successful responses average {avg_successful_exec:.1f} execution points")
            print(f"   • {sum(1 for r in successful_tests if r['appropriate_branding'])} had appropriate A.T.L.A.S. branding")
            print(f"   • {sum(1 for r in successful_tests if r['has_specific_data'])} included specific trading data")

def main():
    """Main execution function"""
    test_suite = ATLASBeginnerTestSuite()
    test_suite.run_all_tests()

if __name__ == "__main__":
    main()
