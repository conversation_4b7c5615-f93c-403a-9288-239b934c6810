#!/usr/bin/env python3
"""
Final test of A.T.L.A.S. trading capabilities
"""

import requests
import json

def test_atlas_trading():
    """Test the exact trading request specified"""
    
    print("🎯 FINAL A.T.L.A.S. TRADING TEST")
    print("=" * 50)
    
    # The exact request specified
    data = {
        'message': 'Find a current TTM Squeeze setup on AAPL and execute a paper trade with specific entry, target, and stop-loss levels.',
        'session_id': 'final_test',
        'context': {
            'panel': 'right',
            'interface_type': 'pattern_scanner'
        }
    }
    
    try:
        print("📡 Sending request to A.T.L.A.S...")
        print(f"🎯 Request: {data['message']}")
        print()
        
        response = requests.post('http://localhost:8080/api/v1/chat', 
                               json=data, 
                               timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print()
            print("💬 A.T.L.A.S. Response:")
            print("=" * 60)
            print(result.get('response', 'No response'))
            print("=" * 60)
            
            # Check for key capabilities
            response_text = result.get('response', '').lower()
            
            print()
            print("🔍 CAPABILITY VERIFICATION:")
            print("=" * 40)
            
            capabilities = {
                'A.T.L.A.S. Branding': 'a.t.l.a.s' in response_text,
                'TTM Squeeze Detection': 'ttm' in response_text or 'squeeze' in response_text,
                'AAPL Analysis': 'aapl' in response_text,
                'Trade Execution': 'trade' in response_text or 'execute' in response_text,
                'Entry Price': 'entry' in response_text,
                'Target Price': 'target' in response_text,
                'Stop Loss': 'stop' in response_text,
                'Paper Trading': 'paper' in response_text,
                'Specific Prices': '$' in result.get('response', ''),
                'Market Analysis': 'analysis' in response_text or 'market' in response_text
            }
            
            for capability, detected in capabilities.items():
                status = "✅ DETECTED" if detected else "❌ MISSING"
                print(f"{capability}: {status}")
            
            print()
            detected_count = sum(capabilities.values())
            total_count = len(capabilities)
            
            print(f"📊 SCORE: {detected_count}/{total_count} capabilities detected")
            
            if detected_count >= 8:
                print("🎉 EXCELLENT: A.T.L.A.S. is fully functional!")
                return True
            elif detected_count >= 6:
                print("✅ GOOD: A.T.L.A.S. is mostly functional")
                return True
            else:
                print("⚠️  NEEDS IMPROVEMENT: Some capabilities missing")
                return False
            
        else:
            print(f"❌ ERROR: HTTP {response.status_code}")
            print("Response:", response.text)
            return False
            
    except Exception as e:
        print(f"💥 ERROR: {e}")
        return False

if __name__ == "__main__":
    success = test_atlas_trading()
    
    print()
    print("=" * 60)
    if success:
        print("🎉 FINAL RESULT: A.T.L.A.S. TRADING SYSTEM IS WORKING!")
        print("✅ System successfully demonstrates trading capabilities")
        print("✅ Ready for comprehensive trading operations")
    else:
        print("⚠️  FINAL RESULT: SYSTEM NEEDS ATTENTION")
        print("❌ Some trading capabilities need fixes")
    print("=" * 60)
