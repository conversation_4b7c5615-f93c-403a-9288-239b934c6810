#!/usr/bin/env python3
"""
A.T.L.A.S. Five-Criteria TTM Squeeze Pattern Detection Algorithm
Advanced pattern recognition for high-confidence trading signals
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class ATLASFiveCriteriaDetector:
    """
    Advanced TTM Squeeze pattern detector using 5-criteria algorithm
    
    Criteria:
    1. Histogram Reversal (3 down → 1 up)
    2. Momentum Confirmation
    3. Weekly Trend Alignment (8-EMA slope up + price above)
    4. Daily Trend Confirmation (same on daily)
    5. Price Above 5-EMA
    Optional: TTM Squeeze (BB inside KC)
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def detect_pattern(self, df: pd.DataFrame, daily_df: pd.DataFrame, 
                      weekly_df: pd.DataFrame, require_squeeze: bool = False) -> Dict[str, Any]:
        """
        Detect 5-criteria TTM Squeeze pattern
        
        Args:
            df: intraday bars (with 'histogram' & 'momentum' precomputed)
            daily_df: daily bars (with 'close')
            weekly_df: weekly bars (with 'close')
            require_squeeze: if True, enforce Bollinger-in-Keltner squeeze
            
        Returns:
            Dict with pattern detection results and confidence score
        """
        try:
            results = {
                "pattern_detected": False,
                "confidence_score": 0.0,
                "criteria_met": {},
                "entry_price": None,
                "target_price": None,
                "stop_loss": None,
                "risk_reward_ratio": None
            }
            
            # Ensure we have enough data
            if len(df) < 5 or len(daily_df) < 10 or len(weekly_df) < 10:
                results["error"] = "Insufficient data for pattern detection"
                return results
            
            # 1. Histogram Reversal (3 down → 1 up)
            cond1, hist_confidence = self._check_histogram_reversal(df)
            results["criteria_met"]["histogram_reversal"] = cond1
            
            # 2. Momentum Confirmation
            cond2, momentum_confidence = self._check_momentum_confirmation(df)
            results["criteria_met"]["momentum_confirmation"] = cond2
            
            # 3. Weekly Trend Alignment
            cond3, weekly_confidence = self._check_weekly_trend(weekly_df)
            results["criteria_met"]["weekly_trend_alignment"] = cond3
            
            # 4. Daily Trend Confirmation
            cond4, daily_confidence = self._check_daily_trend(daily_df)
            results["criteria_met"]["daily_trend_confirmation"] = cond4
            
            # 5. Price Above 5-EMA
            cond5, ema_confidence = self._check_price_above_ema(df)
            results["criteria_met"]["price_above_5ema"] = cond5
            
            # Optional: TTM Squeeze
            cond6, squeeze_confidence = True, 0.8  # Default to True if not required
            if require_squeeze:
                cond6, squeeze_confidence = self._check_ttm_squeeze(df)
                results["criteria_met"]["ttm_squeeze"] = cond6
            
            # Calculate overall confidence
            confidences = [hist_confidence, momentum_confidence, weekly_confidence, 
                          daily_confidence, ema_confidence]
            if require_squeeze:
                confidences.append(squeeze_confidence)
            
            # Pattern detected if all criteria met
            all_criteria_met = all([cond1, cond2, cond3, cond4, cond5, cond6])
            results["pattern_detected"] = all_criteria_met
            
            if all_criteria_met:
                results["confidence_score"] = np.mean(confidences)
                
                # Calculate trading parameters
                current_price = df['close'].iloc[-1]
                results["entry_price"] = current_price
                results["target_price"] = current_price * 1.03  # 3% target
                results["stop_loss"] = current_price * 0.98     # 2% stop
                results["risk_reward_ratio"] = 1.5
                
                # Add pattern strength assessment
                criteria_count = sum([cond1, cond2, cond3, cond4, cond5, cond6])
                if criteria_count == 6:
                    results["pattern_strength"] = "Very Strong"
                elif criteria_count == 5:
                    results["pattern_strength"] = "Strong"
                else:
                    results["pattern_strength"] = "Moderate"
            
            return results
            
        except Exception as e:
            logger.error(f"Pattern detection failed: {e}")
            return {"error": str(e), "pattern_detected": False}
    
    def _check_histogram_reversal(self, df: pd.DataFrame) -> Tuple[bool, float]:
        """Check for 3 declining histogram bars followed by 1 rising bar"""
        try:
            if 'histogram' not in df.columns or len(df) < 4:
                return False, 0.0
            
            h = df['histogram']
            
            # Check for exactly 3 declining bars followed by 1 rising bar
            declining_pattern = (h.iloc[-4] > h.iloc[-3] > h.iloc[-2])
            rising_bar = (h.iloc[-1] > h.iloc[-2])
            
            pattern_met = declining_pattern and rising_bar
            
            # Calculate confidence based on histogram values
            if pattern_met:
                decline_strength = abs(h.iloc[-4] - h.iloc[-2]) / abs(h.iloc[-4])
                rise_strength = abs(h.iloc[-1] - h.iloc[-2]) / abs(h.iloc[-2]) if h.iloc[-2] != 0 else 1.0
                confidence = min(0.95, 0.6 + (decline_strength + rise_strength) * 0.2)
            else:
                confidence = 0.0
            
            return pattern_met, confidence
            
        except Exception as e:
            logger.error(f"Histogram reversal check failed: {e}")
            return False, 0.0
    
    def _check_momentum_confirmation(self, df: pd.DataFrame) -> Tuple[bool, float]:
        """Check for momentum strengthening bar-over-bar"""
        try:
            if 'momentum' not in df.columns or len(df) < 2:
                return False, 0.0
            
            m = df['momentum']
            momentum_rising = m.iloc[-1] > m.iloc[-2]
            
            if momentum_rising:
                momentum_change = abs(m.iloc[-1] - m.iloc[-2])
                confidence = min(0.95, 0.7 + momentum_change * 0.1)
            else:
                confidence = 0.0
            
            return momentum_rising, confidence
            
        except Exception as e:
            logger.error(f"Momentum confirmation check failed: {e}")
            return False, 0.0
    
    def _check_weekly_trend(self, weekly_df: pd.DataFrame) -> Tuple[bool, float]:
        """Check weekly trend alignment (8-EMA slope up + price above)"""
        try:
            if len(weekly_df) < 10:
                return False, 0.0
            
            weekly_ema8 = weekly_df['close'].ewm(span=8).mean()
            
            ema_rising = weekly_ema8.iloc[-1] > weekly_ema8.iloc[-2]
            price_above_ema = weekly_df['close'].iloc[-1] > weekly_ema8.iloc[-1]
            
            trend_aligned = ema_rising and price_above_ema
            
            if trend_aligned:
                ema_slope = (weekly_ema8.iloc[-1] - weekly_ema8.iloc[-3]) / weekly_ema8.iloc[-3]
                price_distance = (weekly_df['close'].iloc[-1] - weekly_ema8.iloc[-1]) / weekly_ema8.iloc[-1]
                confidence = min(0.95, 0.7 + (ema_slope + price_distance) * 2)
            else:
                confidence = 0.0
            
            return trend_aligned, confidence
            
        except Exception as e:
            logger.error(f"Weekly trend check failed: {e}")
            return False, 0.0
    
    def _check_daily_trend(self, daily_df: pd.DataFrame) -> Tuple[bool, float]:
        """Check daily trend confirmation (same as weekly)"""
        try:
            if len(daily_df) < 10:
                return False, 0.0
            
            daily_ema8 = daily_df['close'].ewm(span=8).mean()
            
            ema_rising = daily_ema8.iloc[-1] > daily_ema8.iloc[-2]
            price_above_ema = daily_df['close'].iloc[-1] > daily_ema8.iloc[-1]
            
            trend_aligned = ema_rising and price_above_ema
            
            if trend_aligned:
                ema_slope = (daily_ema8.iloc[-1] - daily_ema8.iloc[-3]) / daily_ema8.iloc[-3]
                price_distance = (daily_df['close'].iloc[-1] - daily_ema8.iloc[-1]) / daily_ema8.iloc[-1]
                confidence = min(0.95, 0.7 + (ema_slope + price_distance) * 2)
            else:
                confidence = 0.0
            
            return trend_aligned, confidence
            
        except Exception as e:
            logger.error(f"Daily trend check failed: {e}")
            return False, 0.0
    
    def _check_price_above_ema(self, df: pd.DataFrame) -> Tuple[bool, float]:
        """Check if price is above 5-EMA"""
        try:
            if len(df) < 5:
                return False, 0.0
            
            ema5 = df['close'].ewm(span=5).mean()
            price_above = df['close'].iloc[-1] > ema5.iloc[-1]
            
            if price_above:
                distance = (df['close'].iloc[-1] - ema5.iloc[-1]) / ema5.iloc[-1]
                confidence = min(0.95, 0.8 + distance * 5)
            else:
                confidence = 0.0
            
            return price_above, confidence
            
        except Exception as e:
            logger.error(f"Price above EMA check failed: {e}")
            return False, 0.0
    
    def _check_ttm_squeeze(self, df: pd.DataFrame) -> Tuple[bool, float]:
        """Check for TTM Squeeze (Bollinger Bands inside Keltner Channels)"""
        try:
            if len(df) < 20:
                return False, 0.0
            
            # Calculate Bollinger Bands
            bb_period = 20
            bb_std = 2
            bb_middle = df['close'].rolling(window=bb_period).mean()
            bb_std_dev = df['close'].rolling(window=bb_period).std()
            bb_upper = bb_middle + (bb_std_dev * bb_std)
            bb_lower = bb_middle - (bb_std_dev * bb_std)
            
            # Calculate Keltner Channels
            kc_period = 20
            kc_mult = 1.5
            kc_middle = df['close'].rolling(window=kc_period).mean()
            tr1 = df['high'] - df['low']
            tr2 = abs(df['high'] - df['close'].shift())
            tr3 = abs(df['low'] - df['close'].shift())
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = true_range.rolling(window=kc_period).mean()
            kc_upper = kc_middle + (atr * kc_mult)
            kc_lower = kc_middle - (atr * kc_mult)
            
            # Check if BB is inside KC (squeeze condition)
            squeeze_condition = (bb_upper.iloc[-1] < kc_upper.iloc[-1] and 
                               bb_lower.iloc[-1] > kc_lower.iloc[-1])
            
            if squeeze_condition:
                # Calculate squeeze intensity
                bb_width = bb_upper.iloc[-1] - bb_lower.iloc[-1]
                kc_width = kc_upper.iloc[-1] - kc_lower.iloc[-1]
                squeeze_intensity = 1 - (bb_width / kc_width)
                confidence = min(0.95, 0.6 + squeeze_intensity * 0.3)
            else:
                confidence = 0.0
            
            return squeeze_condition, confidence
            
        except Exception as e:
            logger.error(f"TTM Squeeze check failed: {e}")
            return False, 0.0
    
    def generate_trading_signal(self, symbol: str, pattern_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive trading signal from pattern detection"""
        if not pattern_result.get("pattern_detected", False):
            return {"signal": "NO_SIGNAL", "reason": "Pattern criteria not met"}
        
        signal = {
            "symbol": symbol,
            "signal": "BUY",
            "pattern": "TTM_SQUEEZE_5_CRITERIA",
            "confidence": pattern_result["confidence_score"],
            "strength": pattern_result.get("pattern_strength", "Moderate"),
            "entry_price": pattern_result["entry_price"],
            "target_price": pattern_result["target_price"],
            "stop_loss": pattern_result["stop_loss"],
            "risk_reward_ratio": pattern_result["risk_reward_ratio"],
            "position_size_recommendation": "Conservative (1-2% of portfolio)",
            "criteria_met": pattern_result["criteria_met"],
            "trading_notes": [
                "5-criteria TTM Squeeze pattern confirmed",
                "Multi-timeframe trend alignment verified",
                "Histogram reversal pattern detected",
                "Momentum confirmation present"
            ]
        }
        
        return signal

def test_five_criteria_detector():
    """Test the five-criteria detector with sample data"""
    print("🎯 Testing A.T.L.A.S. Five-Criteria TTM Squeeze Detector")
    print("=" * 60)
    
    detector = ATLASFiveCriteriaDetector()
    
    # Create sample data for testing
    dates = pd.date_range('2024-01-01', periods=100, freq='1H')
    sample_data = pd.DataFrame({
        'close': np.random.randn(100).cumsum() + 100,
        'high': np.random.randn(100).cumsum() + 102,
        'low': np.random.randn(100).cumsum() + 98,
        'histogram': np.random.randn(100) * 0.5,
        'momentum': np.random.randn(100) * 0.3
    }, index=dates)
    
    # Create sample daily and weekly data
    daily_data = sample_data.resample('D').agg({
        'close': 'last',
        'high': 'max',
        'low': 'min'
    }).dropna()
    
    weekly_data = sample_data.resample('W').agg({
        'close': 'last',
        'high': 'max',
        'low': 'min'
    }).dropna()
    
    # Test pattern detection
    result = detector.detect_pattern(sample_data, daily_data, weekly_data, require_squeeze=True)
    
    print(f"Pattern Detected: {result['pattern_detected']}")
    print(f"Confidence Score: {result.get('confidence_score', 0):.2%}")
    print(f"Criteria Met: {sum(result.get('criteria_met', {}).values())}/6")
    
    if result['pattern_detected']:
        signal = detector.generate_trading_signal("TEST", result)
        print(f"\nTrading Signal Generated:")
        print(f"  Signal: {signal['signal']}")
        print(f"  Entry: ${signal['entry_price']:.2f}")
        print(f"  Target: ${signal['target_price']:.2f}")
        print(f"  Stop: ${signal['stop_loss']:.2f}")
    
    print("\n✅ Five-Criteria Detector Test Complete")

if __name__ == "__main__":
    test_five_criteria_detector()
