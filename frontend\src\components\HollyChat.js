import React, { useState, useRef, useEffect } from 'react';
import {
  Paper,
  TextField,
  Button,
  Box,
  Typography,
  List,
  ListItem,
  Divider,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  CardActions,
  Chip,
  Grid,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Send as SendIcon,
  SmartToy as HollyI<PERSON>,
  Person as PersonIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  AccountBalance as AccountBalanceIcon,
  Refresh as RefreshIcon,
  Help as HelpIcon
} from '@mui/icons-material';

const HollyChat = ({ onPlanExecuted }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: "Hi! I'm <PERSON>, your personal trading assistant. I can help you with anything trading-related - just ask me in plain English!\n\nTry asking me:\n• \"Make me $50 today\"\n• \"What's AAPL looking like?\"\n• \"Find me some momentum plays\"\n• \"I need a hedge for my Tesla position\"",
      timestamp: new Date(),
      response_type: 'chat'
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [quickActions] = useState([
    { label: "Make me $50", message: "Make me $50 today" },
    { label: "Find opportunities", message: "Find me some good trading opportunities" },
    { label: "Market overview", message: "What's the market looking like today?" },
    { label: "TTM Squeeze", message: "Show me some TTM Squeeze setups" }
  ]);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (messageText = null) => {
    const messageToSend = messageText || inputMessage;
    if (!messageToSend.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: messageToSend,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/v1/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageToSend,
          user_context: {
            timestamp: new Date().toISOString(),
            session_id: 'web_session'
          }
        })
      });

      const data = await response.json();

      const hollyMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: data.response,
        timestamp: new Date(),
        response_type: data.type,
        requires_action: data.requires_action,
        trading_plan: data.trading_plan,
        plan_id: data.plan_id,
        function_called: data.function_called
      };

      setMessages(prev => [...prev, hollyMessage]);

    } catch (error) {
      const errorMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: 'Sorry, I encountered an error. Please try again or rephrase your request.',
        timestamp: new Date(),
        response_type: 'error',
        error: true
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExecutePlan = async (planId) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/v1/holly/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan_id: planId,
          confirm: true
        })
      });

      const data = await response.json();

      if (data.success) {
        const successMessage = {
          id: Date.now(),
          type: 'assistant',
          content: `✅ ${data.message}\n\nExecuted orders:\n${data.executed_orders.map(order => 
            `• ${order.side.toUpperCase()} ${order.quantity} ${order.symbol}`
          ).join('\n')}`,
          timestamp: new Date(),
          response_type: 'execution_result'
        };
        setMessages(prev => [...prev, successMessage]);
        
        if (onPlanExecuted) {
          onPlanExecuted();
        }
      } else {
        throw new Error(data.message || 'Failed to execute plan');
      }

    } catch (error) {
      const errorMessage = {
        id: Date.now(),
        type: 'assistant',
        content: `❌ Failed to execute plan: ${error.message}`,
        timestamp: new Date(),
        response_type: 'error',
        error: true
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getResponseIcon = (responseType) => {
    switch (responseType) {
      case 'trading_plan':
        return <TrendingUpIcon fontSize="small" color="primary" />;
      case 'market_analysis':
        return <AssessmentIcon fontSize="small" color="info" />;
      case 'market_data':
        return <AccountBalanceIcon fontSize="small" color="success" />;
      default:
        return <HollyIcon fontSize="small" color="primary" />;
    }
  };

  const renderTradingPlan = (message) => {
    if (!message.trading_plan) return null;

    const plan = message.trading_plan;
    
    return (
      <Card sx={{ mt: 1, backgroundColor: '#e8f5e8', border: '1px solid #4caf50' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
            <TrendingUpIcon color="success" />
            <Typography variant="subtitle2" color="success.main">
              Trading Plan Generated
            </Typography>
          </Box>
          
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="body2">
                <strong>Trades:</strong> {plan.summary?.total_trades || 0}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2">
                <strong>Total Risk:</strong> ${plan.summary?.total_risk || 0}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2">
                <strong>Expected Return:</strong> ${plan.summary?.expected_return || 0}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2">
                <strong>Risk/Reward:</strong> {plan.summary?.risk_reward_ratio || 0}:1
              </Typography>
            </Grid>
          </Grid>

          {plan.trades && plan.trades.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                Planned Trades:
              </Typography>
              {plan.trades.map((trade, index) => (
                <Box key={index} sx={{ mb: 1, p: 1, backgroundColor: 'rgba(255,255,255,0.7)', borderRadius: 1 }}>
                  <Typography variant="body2">
                    <strong>{trade.action.toUpperCase()}</strong> {trade.quantity} {trade.symbol} @ ${trade.entry_price}
                  </Typography>
                  {trade.stop_price && (
                    <Typography variant="caption" color="error">
                      Stop: ${trade.stop_price}
                    </Typography>
                  )}
                  {trade.target_price && (
                    <Typography variant="caption" color="success" sx={{ ml: 2 }}>
                      Target: ${trade.target_price}
                    </Typography>
                  )}
                </Box>
              ))}
            </Box>
          )}
        </CardContent>
        <CardActions>
          <Button 
            size="small" 
            variant="contained" 
            color="success"
            onClick={() => handleExecutePlan(message.plan_id)}
            disabled={isLoading}
            startIcon={<TrendingUpIcon />}
          >
            Execute Plan (Paper Trading)
          </Button>
        </CardActions>
      </Card>
    );
  };

  return (
    <Paper elevation={3} sx={{ height: '700px', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ 
        p: 2, 
        backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        borderBottom: '1px solid #ddd' 
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <HollyIcon />
            <Typography variant="h6">Holly AI</Typography>
            <Chip label="Paper Trading" size="small" color="warning" />
          </Box>
          <Box>
            <Tooltip title="Get help">
              <IconButton 
                size="small" 
                sx={{ color: 'white' }}
                onClick={() => handleSendMessage("What can you help me with?")}
              >
                <HelpIcon />
              </IconButton>
            </Tooltip>
            <Tooltip title="Reset conversation">
              <IconButton 
                size="small" 
                sx={{ color: 'white' }}
                onClick={async () => {
                  try {
                    await fetch('/api/v1/holly/reset', { method: 'POST' });
                    setMessages([{
                      id: 1,
                      type: 'assistant',
                      content: "Hi! I'm Holly AI. How can I help you with trading today?",
                      timestamp: new Date(),
                      response_type: 'chat'
                    }]);
                  } catch (error) {
                    console.error('Failed to reset conversation:', error);
                  }
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>

      {/* Quick Actions */}
      <Box sx={{ p: 1, backgroundColor: '#f8f9fa', borderBottom: '1px solid #eee' }}>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {quickActions.map((action, index) => (
            <Chip
              key={index}
              label={action.label}
              size="small"
              onClick={() => handleSendMessage(action.message)}
              sx={{ cursor: 'pointer' }}
              variant="outlined"
            />
          ))}
        </Box>
      </Box>

      {/* Messages */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>
        <List>
          {messages.map((message) => (
            <ListItem key={message.id} sx={{ flexDirection: 'column', alignItems: 'stretch', py: 1 }}>
              <Box sx={{ 
                display: 'flex', 
                justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
                width: '100%'
              }}>
                <Box sx={{ 
                  maxWidth: '85%',
                  backgroundColor: message.type === 'user' ? '#1976d2' : '#f5f5f5',
                  color: message.type === 'user' ? 'white' : 'black',
                  borderRadius: 2,
                  p: 1.5,
                  mb: 1
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                    {message.type === 'user' ? <PersonIcon fontSize="small" /> : getResponseIcon(message.response_type)}
                    <Typography variant="caption">
                      {formatTime(message.timestamp)}
                    </Typography>
                    {message.function_called && (
                      <Chip label={message.function_called} size="small" variant="outlined" />
                    )}
                  </Box>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                    {message.content}
                  </Typography>
                  
                  {message.error && (
                    <Alert severity="error" sx={{ mt: 1 }}>
                      There was an error processing your request.
                    </Alert>
                  )}
                </Box>
              </Box>

              {/* Render trading plan if present */}
              {renderTradingPlan(message)}
            </ListItem>
          ))}
          {isLoading && (
            <ListItem>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={20} />
                <Typography variant="body2" color="text.secondary">
                  Holly is thinking...
                </Typography>
              </Box>
            </ListItem>
          )}
        </List>
        <div ref={messagesEndRef} />
      </Box>

      <Divider />

      {/* Input */}
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <TextField
            fullWidth
            multiline
            maxRows={3}
            placeholder="Ask Holly anything about trading... (e.g., 'Make me $50 today')"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
            variant="outlined"
            size="small"
          />
          <Button
            variant="contained"
            onClick={() => handleSendMessage()}
            disabled={!inputMessage.trim() || isLoading}
            sx={{ minWidth: 'auto', px: 2 }}
          >
            <SendIcon />
          </Button>
        </Box>
        <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
          Holly understands natural language - just ask what you want to do!
        </Typography>
      </Box>
    </Paper>
  );
};

export default HollyChat;
