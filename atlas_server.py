"""
A.T.L.A.S AI Trading System - Non-Blocking FastAPI Server
Production-ready server with immediate startup and background initialization
"""

import asyncio
import logging
import logging.config
from datetime import datetime
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Query, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles

# Import local modules
from config import settings, LOGGING_CONFIG, validate_environment
from models import (
    ChatRequest, AIResponse, AnalysisRequest, EducationRequest,
    SystemStatus, EngineStatus, InitializationStatus
)

# Configure logging
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)

# Global state
orchestrator = None
initialization_status = {
    "orchestrator": InitializationStatus(
        component="orchestrator",
        status=EngineStatus.INITIALIZING,
        progress=0.0,
        started_at=datetime.now()
    ),
    "ai_engine": InitializationStatus(
        component="ai_engine",
        status=EngineStatus.INITIALIZING,
        progress=0.0,
        started_at=datetime.now()
    ),
    "market_engine": InitializationStatus(
        component="market_engine",
        status=EngineStatus.INITIALIZING,
        progress=0.0,
        started_at=datetime.now()
    ),
    "trading_engine": InitializationStatus(
        component="trading_engine",
        status=EngineStatus.INITIALIZING,
        progress=0.0,
        started_at=datetime.now()
    ),
    "risk_engine": InitializationStatus(
        component="risk_engine",
        status=EngineStatus.INITIALIZING,
        progress=0.0,
        started_at=datetime.now()
    ),
    "education_engine": InitializationStatus(
        component="education_engine",
        status=EngineStatus.INITIALIZING,
        progress=0.0,
        started_at=datetime.now()
    )
}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager with background initialization"""
    logger.info("Starting A.T.L.A.S AI Trading System - Non-Blocking Architecture")
    logger.info("Advanced Trading & Learning Analysis System v4.0")
    logger.info("Server starting immediately, background initialization in progress...")
    
    # Start background initialization
    init_task = asyncio.create_task(initialize_system())
    
    yield  # Server is now running and accepting requests
    
    # Cleanup on shutdown
    logger.info("🛑 Shutting down A.T.L.A.S system...")
    if not init_task.done():
        init_task.cancel()
    
    if orchestrator:
        try:
            await asyncio.wait_for(orchestrator.cleanup(), timeout=10.0)
            logger.info("✅ A.T.L.A.S system shutdown completed")
        except asyncio.TimeoutError:
            logger.warning("⚠️ Shutdown timeout - forcing exit")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


# Initialize FastAPI app with lifespan
app = FastAPI(
    title="A.T.L.A.S AI Trading System",
    description="Advanced Trading & Learning Analysis System with Non-Blocking Architecture",
    version="4.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


async def initialize_system():
    """Background system initialization with progress tracking"""
    global orchestrator
    
    try:
        logger.info("Starting background initialization...")
        
        # Validate environment first
        env_status = validate_environment()
        if not env_status["valid"]:
            logger.error(f"❌ Environment validation failed: {env_status['errors']}")
            for component in initialization_status.values():
                component.status = EngineStatus.FAILED
                component.error = "Environment validation failed"
            return
        
        # Import orchestrator (lazy import to avoid blocking)
        logger.info("Importing AtlasOrchestrator...")
        from atlas_orchestrator import AtlasOrchestrator
        
        # Initialize orchestrator
        logger.info("Initializing AtlasOrchestrator...")
        initialization_status["orchestrator"].progress = 0.1
        
        orchestrator = AtlasOrchestrator()
        initialization_status["orchestrator"].progress = 0.5
        
        # Initialize components with progress tracking
        await orchestrator.initialize_with_progress(update_progress_callback)
        
        # Mark orchestrator as complete
        initialization_status["orchestrator"].status = EngineStatus.ACTIVE
        initialization_status["orchestrator"].progress = 1.0
        initialization_status["orchestrator"].completed_at = datetime.now()
        
        logger.info("✅ A.T.L.A.S system initialization completed successfully")
        
    except Exception as e:
        logger.error(f"❌ System initialization failed: {e}")
        
        # Mark all components as failed
        for component in initialization_status.values():
            if component.status == EngineStatus.INITIALIZING:
                component.status = EngineStatus.FAILED
                component.error = str(e)


def update_progress_callback(component: str, progress: float, status: EngineStatus, message: str = None):
    """Callback to update initialization progress"""
    if component in initialization_status:
        initialization_status[component].progress = progress
        initialization_status[component].status = status
        if message:
            initialization_status[component].message = message
        if status in [EngineStatus.ACTIVE, EngineStatus.FAILED]:
            initialization_status[component].completed_at = datetime.now()


# Core API Endpoints

@app.get("/")
async def root():
    """Root endpoint - serve the main interface"""
    try:
        return FileResponse("atlas_interface.html")
    except FileNotFoundError:
        return HTMLResponse("""
        <html>
            <head><title>A.T.L.A.S AI Trading System</title></head>
            <body>
                <h1>🚀 A.T.L.A.S AI Trading System</h1>
                <p>Advanced Trading & Learning Analysis System</p>
                <p>Status: <span id="status">Initializing...</span></p>
                <script>
                    setInterval(async () => {
                        try {
                            const response = await fetch('/api/v1/health');
                            const data = await response.json();
                            document.getElementById('status').textContent = data.status;
                        } catch (e) {
                            document.getElementById('status').textContent = 'Error';
                        }
                    }, 2000);
                </script>
            </body>
        </html>
        """)


@app.get("/atlas_interface.html")
async def interface():
    """Serve the main interface file"""
    try:
        return FileResponse("atlas_interface.html")
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail="Interface file not found")


@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint - always responds within 1 second"""
    try:
        # Calculate overall status
        active_count = sum(1 for status in initialization_status.values() 
                          if status.status == EngineStatus.ACTIVE)
        failed_count = sum(1 for status in initialization_status.values() 
                          if status.status == EngineStatus.FAILED)
        total_count = len(initialization_status)
        
        if failed_count > 0:
            overall_status = "degraded"
        elif active_count == total_count:
            overall_status = "healthy"
        else:
            overall_status = "initializing"
        
        # Calculate overall progress
        total_progress = sum(status.progress for status in initialization_status.values())
        overall_progress = total_progress / total_count
        
        return SystemStatus(
            status=overall_status,
            timestamp=datetime.now(),
            engines={name: status.status for name, status in initialization_status.items()},
            initialization_progress={
                "overall": overall_progress,
                **{name: status.progress for name, status in initialization_status.items()}
            },
            errors=[status.error for status in initialization_status.values() 
                   if status.error] or None,
            warnings=validate_environment().get("warnings") or None
        )
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return SystemStatus(
            status="failed",
            timestamp=datetime.now(),
            engines={name: EngineStatus.FAILED for name in initialization_status.keys()},
            errors=[str(e)]
        )


@app.post("/api/v1/chat")
async def predicto_chat_endpoint(request: ChatRequest) -> AIResponse:
    """Predicto - Primary Conversational AI Interface for Stock Analysis"""
    try:
        # Check if orchestrator is ready
        if not orchestrator:
            return AIResponse(
                response="🔮 Predicto is initializing. Please wait a moment while I set up my stock analysis capabilities...",
                type="system_status",
                confidence=0.5,
                context={"initialization_progress": {
                    name: status.progress for name, status in initialization_status.items()
                }}
            )

        logger.info(f"Predicto processing: {request.message[:100]}...")

        # Process message through Predicto-powered orchestrator with timeout
        response = await asyncio.wait_for(
            orchestrator.process_message(
                message=request.message,
                session_id=request.session_id
            ),
            timeout=30.0
        )

        # Add A.T.L.A.S branding to response context
        if response.context is None:
            response.context = {}
        response.context["powered_by"] = "A.T.L.A.S powered by Predicto"
        response.context["system"] = "Advanced Trading & Learning Analysis System"
        response.context["interface"] = "Predicto Conversational AI"
        response.context["capabilities"] = "25+ A.T.L.A.S features accessible through natural conversation"

        return response
        
    except asyncio.TimeoutError:
        logger.error("Predicto processing timeout")
        return AIResponse(
            response="I apologize, but your stock analysis request is taking longer than expected. Please try again with a simpler question, or ask me to analyze a specific symbol.",
            type="timeout",
            confidence=0.3,
            context={"powered_by": "Predicto - AI Stock Analysis Expert"}
        )
    except Exception as e:
        logger.error(f"Predicto endpoint error: {e}")
        return AIResponse(
            response="I encountered an error processing your request. As your AI stock analysis expert, I'm here to help with market analysis, trading insights, and investment research. Please try again.",
            type="error",
            confidence=0.0,
            context={"error": str(e), "powered_by": "Predicto - AI Stock Analysis Expert"}
        )


@app.get("/api/v1/predicto/capabilities")
async def get_predicto_capabilities():
    """Get Predicto's available capabilities and features"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="Predicto is still initializing")

        # Get AI engine to access Predicto components
        ai_engine = await orchestrator._ensure_ai_engine()

        capabilities = {
            "predicto_status": "active" if ai_engine and ai_engine.predicto_engine else "limited",
            "core_expertise": {
                "stock_analysis": "Advanced technical and fundamental analysis",
                "market_intelligence": "Real-time market scanning and opportunity detection",
                "sentiment_analysis": "Multi-source sentiment with DistilBERT AI",
                "price_predictions": "LSTM neural networks and Predicto forecasting",
                "options_trading": "Greeks calculation and strategy analysis",
                "portfolio_optimization": "Deep learning portfolio optimization",
                "risk_management": "Comprehensive risk assessment and protection",
                "educational_content": "Access to 5 integrated trading books"
            },
            "natural_language_access": {
                "total_features": 25,
                "conversation_examples": [
                    "Analyze AAPL for trading opportunities",
                    "Scan for TTM Squeeze signals",
                    "What's the sentiment on TSLA?",
                    "Predict NVDA price movement",
                    "Best options strategy for earnings?",
                    "Optimize my portfolio allocation",
                    "Explain technical analysis concepts"
                ]
            },
            "advanced_features": [
                "Contextual conversation flow management",
                "Intelligent feature transitions",
                "Proactive market insights",
                "Educational explanations",
                "Risk-first recommendations",
                "Multi-timeframe analysis"
            ]
        }

        if ai_engine and ai_engine.unified_access_layer:
            detailed_capabilities = ai_engine.unified_access_layer.get_available_capabilities()
            capabilities["detailed_features"] = detailed_capabilities

        return capabilities

    except Exception as e:
        logger.error(f"Predicto capabilities error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/predicto/analyze")
async def predicto_stock_analysis(symbol: str = Body(..., embed=True)):
    """Predicto's comprehensive stock analysis"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="Predicto is still initializing")

        # Use Predicto's stock intelligence hub for comprehensive analysis
        ai_engine = await orchestrator._ensure_ai_engine()

        if ai_engine and ai_engine.stock_intelligence_hub:
            analysis = await ai_engine.stock_intelligence_hub.analyze_stock_comprehensive(symbol, orchestrator)
            return {
                "symbol": symbol,
                "analysis": analysis,
                "powered_by": "Predicto Stock Intelligence Hub",
                "timestamp": datetime.now()
            }
        else:
            # Fallback to basic analysis
            market_engine = await orchestrator._ensure_market_engine()
            quote = await market_engine.get_quote(symbol)
            return {
                "symbol": symbol,
                "basic_analysis": {"quote": quote},
                "note": "Predicto Stock Intelligence Hub not available - using basic analysis",
                "timestamp": datetime.now()
            }

    except Exception as e:
        logger.error(f"Predicto stock analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/quote/{symbol}")
async def get_quote(symbol: str):
    """Get real-time quote with caching"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        quote = await orchestrator.market_engine.get_quote(symbol.upper())
        return quote

    except Exception as e:
        logger.error(f"Quote endpoint error for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/scan")
async def market_scan(min_strength: str = Query("moderate")):
    """TTM Squeeze scanner with configurable filters"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        results = await orchestrator.market_engine.scan_market(min_strength)
        return {"signals": results, "count": len(results), "timestamp": datetime.now()}

    except Exception as e:
        logger.error(f"Market scan error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/predicto/forecast/{symbol}")
async def get_predicto_forecast(symbol: str, days: int = Query(5, ge=1, le=30)):
    """Predicto AI predictions with fallback"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        forecast = await orchestrator.market_engine.get_predicto_forecast(symbol.upper(), days)
        return forecast

    except Exception as e:
        logger.error(f"Predicto forecast error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/education")
async def education_query(request: EducationRequest):
    """RAG-based educational queries"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        response = await orchestrator.education_engine.process_query(request)
        return response

    except Exception as e:
        logger.error(f"Education query error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/portfolio")
async def get_portfolio():
    """Trading positions and P&L"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        portfolio = await orchestrator.trading_engine.get_portfolio_summary()
        return portfolio

    except Exception as e:
        logger.error(f"Portfolio endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/risk-assessment")
async def risk_assessment(request: AnalysisRequest):
    """AI-enhanced risk analysis"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        assessment = await orchestrator.risk_engine.assess_risk(request)
        return assessment

    except Exception as e:
        logger.error(f"Risk assessment error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/initialization/status")
async def get_initialization_status():
    """Get detailed initialization status"""
    return {
        "components": {name: {
            "status": status.status,
            "progress": status.progress,
            "message": status.message,
            "error": status.error,
            "started_at": status.started_at,
            "completed_at": status.completed_at
        } for name, status in initialization_status.items()},
        "timestamp": datetime.now()
    }


@app.get("/api/v1/market/news/{symbol}")
async def get_market_news(symbol: str, query_type: str = "news"):
    """Get market news and sentiment for a symbol"""
    try:
        if not orchestrator:
            return {"error": "Market engine not available", "success": False}

        # Search for market context
        news_data = await orchestrator.market_engine.search_market_context(symbol.upper(), query_type)

        return {
            "symbol": symbol.upper(),
            "query_type": query_type,
            "timestamp": datetime.now().isoformat(),
            **news_data
        }

    except Exception as e:
        logger.error(f"Market news API error for {symbol}: {e}")
        return {
            "error": f"Failed to fetch news for {symbol}",
            "details": str(e),
            "success": False
        }


@app.get("/api/v1/market/context/{symbol}")
async def get_market_context(symbol: str):
    """Get comprehensive market context including news, sentiment, and analysis"""
    try:
        if not orchestrator:
            return {"error": "Market engine not available", "success": False}

        # Get multiple types of context
        context_types = ["news", "earnings", "analyst"]
        context_data = {}

        for context_type in context_types:
            try:
                data = await orchestrator.market_engine.search_market_context(symbol.upper(), context_type)
                context_data[context_type] = data
            except Exception as e:
                logger.warning(f"Failed to get {context_type} context for {symbol}: {e}")
                context_data[context_type] = {"success": False, "error": str(e)}

        # Calculate overall sentiment
        overall_sentiment = 0.0
        sentiment_count = 0

        for data in context_data.values():
            if data.get("success") and "sentiment_score" in data:
                overall_sentiment += data["sentiment_score"]
                sentiment_count += 1

        if sentiment_count > 0:
            overall_sentiment = overall_sentiment / sentiment_count

        return {
            "symbol": symbol.upper(),
            "timestamp": datetime.now().isoformat(),
            "overall_sentiment": overall_sentiment,
            "context": context_data,
            "success": True
        }

    except Exception as e:
        logger.error(f"Market context API error for {symbol}: {e}")
        return {
            "error": f"Failed to fetch market context for {symbol}",
            "details": str(e),
            "success": False
        }


@app.get("/api/v1/portfolio/risk-analysis")
async def get_portfolio_risk_analysis():
    """Get comprehensive portfolio risk analysis and hedging suggestions"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        risk_analysis = await orchestrator.trading_engine.analyze_portfolio_risk()
        return {
            "timestamp": datetime.now().isoformat(),
            "risk_analysis": risk_analysis,
            "success": True
        }

    except Exception as e:
        logger.error(f"Portfolio risk analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/portfolio/hedging/{symbol}")
async def get_hedging_strategies(symbol: str, position_size: float = Query(...)):
    """Get hedging strategy suggestions for a specific position"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        strategies = await orchestrator.trading_engine.suggest_hedging_strategies(symbol.upper(), position_size)
        return {
            "symbol": symbol.upper(),
            "position_size": position_size,
            "hedging_strategies": strategies,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

    except Exception as e:
        logger.error(f"Hedging strategies error for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/portfolio/auto-reinvestment")
async def enable_auto_reinvestment(settings: Dict[str, Any] = Body(...)):
    """Enable automatic dividend and profit reinvestment"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        success = await orchestrator.trading_engine.enable_auto_reinvestment(settings)
        return {
            "enabled": success,
            "settings": settings,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

    except Exception as e:
        logger.error(f"Auto-reinvestment setup error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/portfolio/optimization")
async def get_portfolio_optimization():
    """Get portfolio optimization analysis and rebalancing suggestions"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        optimization = await orchestrator.trading_engine.optimize_portfolio_allocation()
        return {
            "optimization_analysis": optimization,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

    except Exception as e:
        logger.error(f"Portfolio optimization error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/trading/prepare-trade")
async def prepare_trade_confirmation(
    symbol: str = Body(...),
    action: str = Body(...),
    quantity: int = Body(...),
    stop_loss: Optional[float] = Body(None),
    profit_target: Optional[float] = Body(None)
):
    """Prepare trade for user confirmation with comprehensive risk analysis"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        trade_details = await orchestrator.trading_engine.prepare_trade_for_confirmation(
            symbol.upper(), action.upper(), quantity, stop_loss, profit_target
        )

        # Generate user-friendly confirmation message
        confirmation_message = orchestrator.trading_engine.generate_trade_confirmation_message(trade_details)

        return {
            "trade_details": trade_details,
            "confirmation_message": confirmation_message,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

    except Exception as e:
        logger.error(f"Trade preparation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/v1/trading/confirm-trade")
async def confirm_trade_execution(
    trade_id: str = Body(...),
    user_confirmed: bool = Body(...)
):
    """Execute trade after user confirmation"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        result = await orchestrator.trading_engine.confirm_trade_execution(trade_id, user_confirmed)

        return {
            "result": result,
            "timestamp": datetime.now().isoformat(),
            "success": result.get("success", False)
        }

    except Exception as e:
        logger.error(f"Trade confirmation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/v1/trading/pending-trades")
async def get_pending_trades():
    """Get all pending trade confirmations"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        pending_trades = orchestrator.trading_engine.pending_trades

        return {
            "pending_trades": pending_trades,
            "count": len(pending_trades),
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

    except Exception as e:
        logger.error(f"Pending trades retrieval error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    
    print("🚀 Starting A.T.L.A.S AI Trading System...")
    print("⚡ Non-blocking architecture - server responds immediately")
    print(f"🌐 Server will be available at: http://localhost:{settings.PORT}")
    print("📊 Health check: http://localhost:{}/api/v1/health".format(settings.PORT))
    print("📚 API docs: http://localhost:{}/docs".format(settings.PORT))
    
    uvicorn.run(
        "atlas_server:app",
        host="0.0.0.0",
        port=settings.PORT,
        reload=False,  # Disable reload for production
        log_level=settings.LOG_LEVEL.lower()
    )
