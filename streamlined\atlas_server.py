"""
A.T.L.A.S AI Trading System - Streamlined Server
Consolidated FastAPI server with unified orchestrator architecture
"""

import asyncio
import logging
import logging.config
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException, Query, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel

# Import consolidated modules
from config import settings, LOGGING_CONFIG
from models import ChatMessage, AIResponse
from atlas_orchestrator import AtlasOrchestrator

# Configure logging
logging.config.dictConfig(LOGGING_CONFIG)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="A.T.L.A.S AI Trading System - Streamlined",
    description="Advanced Trading & Learning Analysis System with Consolidated Architecture",
    version="3.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response Models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

class QuoteRequest(BaseModel):
    symbol: str

class AnalysisRequest(BaseModel):
    symbol: str
    timeframe: str = "1day"

class UserProfileUpdate(BaseModel):
    experience_level: Optional[str] = None
    risk_tolerance: Optional[str] = None
    account_size: Optional[float] = None
    communication_style: Optional[str] = None

# Global A.T.L.A.S Orchestrator - Initialize during startup
atlas_orchestrator = None

@app.on_event("startup")
async def startup_event():
    """Initialize A.T.L.A.S system on startup with timeout protection"""
    global atlas_orchestrator

    logger.info("🚀 Starting A.T.L.A.S AI Trading System - Streamlined Architecture")
    logger.info("🎯 Advanced Trading & Learning Analysis System")
    logger.info("🧠 Consolidated AI Engine Architecture")

    try:
        # Initialize orchestrator with timeout protection
        logger.info("🔧 Initializing AtlasOrchestrator...")
        atlas_orchestrator = AtlasOrchestrator(mentor_mode=True)
        logger.info("✅ AtlasOrchestrator initialized successfully")

        # Start A.T.L.A.S session
        session_id = await atlas_orchestrator.start_session()
        logger.info(f"✅ A.T.L.A.S system initialized with session: {session_id}")

        # Start real-time TTM Squeeze scanner with timeout
        logger.info("🔧 Starting real-time scanner...")
        await asyncio.wait_for(
            atlas_orchestrator.market_engine.start_realtime_scanner(),
            timeout=30.0
        )
        logger.info("🎯 Real-time TTM Squeeze scanner started automatically")

    except asyncio.TimeoutError:
        logger.error("❌ Startup timeout - continuing with limited functionality")
    except Exception as e:
        logger.error(f"❌ Failed to start real-time scanner: {e}")
        logger.info("🔧 System will continue with basic functionality")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("🛑 Shutting down A.T.L.A.S system...")

    try:
        # Stop real-time scanner
        await atlas_orchestrator.market_engine.cleanup()
        logger.info("✅ A.T.L.A.S system shutdown completed")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

@app.get("/")
async def root():
    """Root endpoint - serve the main interface"""
    return FileResponse("atlas_interface.html")

@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check if orchestrator is initialized
        if not atlas_orchestrator:
            return {
                "status": "initializing",
                "timestamp": datetime.utcnow().isoformat(),
                "version": "3.0.0",
                "architecture": "consolidated",
                "message": "A.T.L.A.S system is starting up...",
                "engines": {
                    "ai_engine": "initializing",
                    "trading_engine": "initializing",
                    "risk_engine": "initializing",
                    "market_engine": "initializing",
                    "education_engine": "initializing"
                }
            }

        system_status = await atlas_orchestrator.get_system_status()

        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "3.0.0",
            "architecture": "consolidated",
            "system_status": system_status,
            "engines": {
                "ai_engine": "active",
                "trading_engine": "active",
                "risk_engine": "active",
                "market_engine": "active",
                "education_engine": "active"
            },
            "features": {
                "paper_trading": True,
                "real_time_data": True,
                "ai_analysis": True,
                "risk_management": True,
                "chain_of_thought": True,
                "multi_agent_analysis": True,
                "profit_optimization": True,
                "educational_rag": True,
                "conversational_interface": True,
                "predicto_integration": True
            }
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "degraded",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.post("/api/v1/chat")
async def chat_endpoint(request: ChatRequest) -> AIResponse:
    """Main conversational interface endpoint"""
    try:
        # Check if orchestrator is initialized
        if not atlas_orchestrator:
            return AIResponse(
                response="A.T.L.A.S system is still initializing. Please wait a moment and try again.",
                type="system_status",
                confidence=0.5,
                timestamp=datetime.utcnow()
            )

        logger.info(f"💬 Processing chat message: {request.message[:100]}...")

        # Process message through A.T.L.A.S orchestrator with context
        response = await atlas_orchestrator.process_message(
            message=request.message,
            session_id=request.session_id,
            context=request.context
        )

        return response

    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")
        return AIResponse(
            response="I apologize, but I encountered an error processing your request. Please try again.",
            type="error",
            confidence=0.0,
            timestamp=datetime.utcnow()
        )

@app.get("/api/v1/quote/{symbol}")
async def get_quote(symbol: str):
    """Get real-time quote through market engine"""
    try:
        # Get comprehensive analysis including quote
        analysis = await atlas_orchestrator.market_engine.get_comprehensive_analysis(symbol)
        
        if "error" in analysis:
            raise HTTPException(status_code=500, detail=analysis["error"])
        
        return analysis["quote"]
        
    except Exception as e:
        logger.error(f"Quote endpoint error for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/analysis")
async def get_analysis(request: AnalysisRequest):
    """Get comprehensive market analysis"""
    try:
        # Get comprehensive analysis through market engine
        analysis = await atlas_orchestrator.market_engine.get_comprehensive_analysis(request.symbol)
        
        if "error" in analysis:
            raise HTTPException(status_code=500, detail=analysis["error"])
        
        return analysis
        
    except Exception as e:
        logger.error(f"Analysis endpoint error for {request.symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/portfolio")
async def get_portfolio():
    """Get portfolio status"""
    try:
        portfolio_status = await atlas_orchestrator.trading_engine.get_portfolio_status()
        return portfolio_status
        
    except Exception as e:
        logger.error(f"Portfolio endpoint error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/scan")
async def market_scan():
    """Scan market for opportunities"""
    try:
        scan_results = await atlas_orchestrator.market_engine.scan_market_opportunities()
        return scan_results

    except Exception as e:
        logger.error(f"Market scan error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/realtime-scanner/status")
async def get_scanner_status():
    """Get real-time scanner status"""
    try:
        status = atlas_orchestrator.market_engine.get_scanner_status()
        return status

    except Exception as e:
        logger.error(f"Scanner status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/realtime-scanner/start")
async def start_scanner():
    """Start real-time TTM Squeeze scanner"""
    try:
        await atlas_orchestrator.market_engine.start_realtime_scanner()
        return {"message": "Real-time scanner started successfully", "status": "running"}

    except Exception as e:
        logger.error(f"Start scanner error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/realtime-scanner/stop")
async def stop_scanner():
    """Stop real-time TTM Squeeze scanner"""
    try:
        atlas_orchestrator.market_engine.stop_realtime_scanner()
        return {"message": "Real-time scanner stopped successfully", "status": "stopped"}

    except Exception as e:
        logger.error(f"Stop scanner error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/realtime-scanner/signals")
async def get_live_signals(min_strength: int = Query(3, ge=1, le=5)):
    """Get current live TTM Squeeze signals"""
    try:
        signals = atlas_orchestrator.market_engine.get_live_ttm_signals(min_strength)
        return {
            "signals": signals,
            "count": len(signals),
            "min_strength": min_strength,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Get signals error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/market-context")
async def get_market_context(force_refresh: bool = Query(False)):
    """Get current market context"""
    try:
        context = await atlas_orchestrator.market_engine.get_market_context(force_refresh)
        return context

    except Exception as e:
        logger.error(f"Market context error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/market-context/summary")
async def get_market_context_summary():
    """Get human-readable market context summary"""
    try:
        summary = atlas_orchestrator.market_engine.get_market_context_summary()
        return {"summary": summary}

    except Exception as e:
        logger.error(f"Context summary error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/market-context/implications")
async def get_trading_implications():
    """Get trading implications based on market context"""
    try:
        implications = atlas_orchestrator.market_engine.get_trading_implications()
        return {"implications": implications}

    except Exception as e:
        logger.error(f"Trading implications error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/market-context/earnings/{symbol}")
async def check_earnings_warning(symbol: str):
    """Check if symbol has earnings this week"""
    try:
        has_earnings = await atlas_orchestrator.market_engine.check_earnings_warning(symbol.upper())
        return {
            "symbol": symbol.upper(),
            "has_earnings_this_week": has_earnings,
            "warning": "Earnings announcement this week - increased volatility expected" if has_earnings else None
        }

    except Exception as e:
        logger.error(f"Earnings check error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/market-context/sectors")
async def get_sector_performance():
    """Get current sector rotation performance"""
    try:
        sectors = await atlas_orchestrator.market_engine.get_sector_performance()
        return {
            "sector_performance": sectors,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Sector performance error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/proactive-assistant/status")
async def get_proactive_status():
    """Get proactive assistant status"""
    try:
        status = atlas_orchestrator.market_engine.get_proactive_status()
        return status

    except Exception as e:
        logger.error(f"Proactive status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/proactive-assistant/alerts")
async def get_active_alerts(alert_type: str = Query(None)):
    """Get active proactive alerts"""
    try:
        # Convert string to AlertType if provided
        alert_type_enum = None
        if alert_type:
            from atlas_proactive_assistant import AlertType
            try:
                alert_type_enum = AlertType(alert_type)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid alert type: {alert_type}")

        alerts = atlas_orchestrator.market_engine.get_active_alerts(alert_type_enum)
        return {
            "alerts": alerts,
            "count": len(alerts),
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Get alerts error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/proactive-assistant/alert")
async def send_custom_alert(
    title: str = Body(...),
    message: str = Body(...),
    priority: str = Body("medium")
):
    """Send custom proactive alert"""
    try:
        success = await atlas_orchestrator.market_engine.send_custom_alert(title, message, priority)
        return {
            "success": success,
            "message": "Alert sent successfully" if success else "Failed to send alert"
        }

    except Exception as e:
        logger.error(f"Send alert error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/performance/summary")
async def get_performance_summary(hours: int = Query(1, ge=1, le=24)):
    """Get performance summary for the last N hours"""
    try:
        from atlas_performance_optimizer import performance_optimizer
        summary = performance_optimizer.get_performance_summary(hours)
        return summary

    except Exception as e:
        logger.error(f"Performance summary error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/performance/optimize")
async def optimize_system():
    """Run system optimization"""
    try:
        from atlas_performance_optimizer import performance_optimizer
        result = performance_optimizer.optimize_system()
        return result

    except Exception as e:
        logger.error(f"System optimization error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/v1/performance/cache")
async def clear_caches():
    """Clear all performance caches"""
    try:
        from atlas_performance_optimizer import performance_optimizer
        performance_optimizer.clear_all_caches()
        return {"message": "All caches cleared successfully"}

    except Exception as e:
        logger.error(f"Cache clear error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Advanced ML Modules Endpoints

@app.get("/api/v1/ml/predictions/{symbol}")
async def get_ml_predictions(symbol: str):
    """Get ML predictions for symbol"""
    try:
        predictions = await atlas_orchestrator.market_engine.get_ml_predictions(symbol.upper())
        return predictions

    except Exception as e:
        logger.error(f"ML predictions error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/ttm-squeeze/{symbol}")
async def get_ttm_squeeze_data(symbol: str, timeframe: str = Query("1day", regex="^(1min|5min|15min|30min|1hour|1day)$")):
    """Get TTM Squeeze chart data with indicators"""
    try:
        if not atlas_orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        # Get comprehensive TTM squeeze analysis
        ttm_data = await atlas_orchestrator.market_engine.get_ttm_squeeze_chart_data(symbol.upper(), timeframe)

        return {
            "success": True,
            "symbol": symbol.upper(),
            "timeframe": timeframe,
            "data": ttm_data,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"TTM Squeeze data error for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/test/capabilities")
async def test_system_capabilities():
    """Test endpoint to verify all A.T.L.A.S. capabilities are accessible"""
    try:
        if not atlas_orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        capabilities_test = {
            "system_status": "active",
            "available_capabilities": [
                "Stock Analysis & Technical Indicators",
                "TTM Squeeze Pattern Detection",
                "Real-time Market Scanning",
                "Trade Execution (Paper Mode)",
                "Portfolio Management",
                "Risk Assessment",
                "Alert Management",
                "Sentiment Analysis",
                "LSTM Price Predictions",
                "Options Analysis",
                "Market Context Intelligence",
                "Educational Resources",
                "Conversational AI Interface"
            ],
            "backend_engines": {
                "market_engine": "active",
                "ai_engine": "active",
                "trading_engine": "active",
                "risk_engine": "active",
                "education_engine": "active"
            },
            "predicto_integration": {
                "status": "active",
                "dual_panel_support": True,
                "pattern_scanner": True,
                "trade_execution": True
            },
            "test_queries": [
                "Analyze AAPL stock",
                "Scan for TTM squeeze signals",
                "Execute trade for TSLA",
                "Set up alerts for MSFT",
                "Show market sentiment",
                "What's my portfolio status?"
            ]
        }

        return capabilities_test

    except Exception as e:
        logger.error(f"Capabilities test error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/test/end-to-end")
async def test_end_to_end_functionality(test_request: ChatRequest):
    """Test end-to-end functionality from chat input to system execution"""
    try:
        if not atlas_orchestrator:
            raise HTTPException(status_code=503, detail="System still initializing")

        # Test the full pipeline
        test_message = test_request.message or "Test A.T.L.A.S. system capabilities"

        # Process through the full A.T.L.A.S. pipeline
        response = await atlas_orchestrator.process_message(
            message=test_message,
            session_id="test_session",
            context=test_request.context
        )

        # Verify response quality
        test_results = {
            "input_message": test_message,
            "response_received": bool(response.response),
            "response_type": response.type,
            "confidence": response.confidence,
            "context_included": bool(response.context),
            "system_branding": "A.T.L.A.S. powered by Predicto" in response.response,
            "response_length": len(response.response),
            "timestamp": response.timestamp.isoformat() if response.timestamp else None,
            "full_response": response.response,
            "backend_integration": "success",
            "predicto_routing": True
        }

        return test_results

    except Exception as e:
        logger.error(f"End-to-end test error: {e}")
        return {
            "backend_integration": "failed",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

@app.post("/api/v1/ml/execute/{symbol}")
async def execute_ml_signals(symbol: str):
    """Execute ML trading signals for symbol"""
    try:
        execution_results = await atlas_orchestrator.market_engine.execute_ml_signals(symbol.upper())
        return execution_results

    except Exception as e:
        logger.error(f"ML execution error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/ml/status")
async def get_ml_status():
    """Get status of all ML modules"""
    try:
        status = await atlas_orchestrator.market_engine.get_ml_status()
        return status

    except Exception as e:
        logger.error(f"ML status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/portfolio/optimize")
async def optimize_portfolio(force_rebalance: bool = Body(False)):
    """Run portfolio optimization"""
    try:
        result = await atlas_orchestrator.market_engine.optimize_portfolio(force_rebalance)
        return result

    except Exception as e:
        logger.error(f"Portfolio optimization error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/lstm/status")
async def get_lstm_status():
    """Get LSTM predictor status"""
    try:
        if atlas_orchestrator.market_engine.lstm_predictor:
            status = await atlas_orchestrator.market_engine.lstm_predictor.get_model_status()
            return status
        else:
            return {"error": "LSTM predictor not initialized"}

    except Exception as e:
        logger.error(f"LSTM status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/lstm/train")
async def train_lstm_model():
    """Train LSTM model"""
    try:
        if atlas_orchestrator.market_engine.lstm_predictor:
            # Get S&P 500 symbols for training
            training_symbols = ['SPY', 'QQQ', 'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX']
            metrics = await atlas_orchestrator.market_engine.lstm_predictor.train_model(training_symbols)
            return {"success": True, "metrics": metrics.__dict__ if metrics else None}
        else:
            return {"error": "LSTM predictor not initialized"}

    except Exception as e:
        logger.error(f"LSTM training error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/options/opportunities")
async def get_options_opportunities(limit: int = Query(10, ge=1, le=50)):
    """Get top options flow opportunities"""
    try:
        if atlas_orchestrator.market_engine.options_flow_analyzer:
            opportunities = await atlas_orchestrator.market_engine.options_flow_analyzer.get_top_options_opportunities(limit)
            return {
                "opportunities": [opp.to_dict() for opp in opportunities],
                "count": len(opportunities),
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {"error": "Options flow analyzer not initialized"}

    except Exception as e:
        logger.error(f"Options opportunities error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/rl/train")
async def train_rl_agent(episodes: int = Body(100)):
    """Train RL execution optimizer"""
    try:
        if atlas_orchestrator.market_engine.rl_execution_optimizer:
            await atlas_orchestrator.market_engine.rl_execution_optimizer.train_agent(episodes)
            return {"success": True, "episodes": episodes}
        else:
            return {"error": "RL execution optimizer not initialized"}

    except Exception as e:
        logger.error(f"RL training error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Predicto API Endpoints

@app.get("/api/v1/predicto/forecast/{symbol}")
async def get_predicto_forecast(symbol: str, days: int = Query(5, ge=1, le=30)):
    """Get Predicto AI forecast for symbol"""
    try:
        forecast = await atlas_orchestrator.market_engine.predicto.get_forecast(symbol.upper(), days)

        if forecast:
            return {
                "success": True,
                "symbol": symbol.upper(),
                "forecast": forecast,
                "days": days,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "success": False,
                "error": "Unable to get forecast",
                "symbol": symbol.upper()
            }

    except Exception as e:
        logger.error(f"Predicto forecast error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/predicto/sentiment")
async def get_predicto_market_sentiment():
    """Get Predicto market sentiment analysis"""
    try:
        sentiment = await atlas_orchestrator.market_engine.predicto.get_market_sentiment()

        return {
            "success": True,
            "sentiment": sentiment,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Predicto sentiment error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/predicto/enhanced-prediction/{symbol}")
async def get_enhanced_prediction(
    symbol: str,
    timeframe: str = Query("1day"),
    include_sentiment: bool = Query(True)
):
    """Get enhanced prediction combining Predicto AI with market analysis"""
    try:
        prediction = await atlas_orchestrator.market_engine.predicto.get_enhanced_prediction(
            symbol.upper(), timeframe, include_sentiment
        )

        return prediction

    except Exception as e:
        logger.error(f"Enhanced prediction error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/predicto/status")
async def get_predicto_status():
    """Get Predicto integration status"""
    try:
        predicto = atlas_orchestrator.market_engine.predicto

        return {
            "enabled": predicto.enabled,
            "api_key_configured": bool(predicto.api_key),
            "base_url": predicto.base_url,
            "status": "active" if predicto.enabled else "disabled"
        }

    except Exception as e:
        logger.error(f"Predicto status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Predicto API Endpoints

@app.get("/api/v1/predicto/forecast/{symbol}")
async def get_predicto_forecast(symbol: str, days: int = Query(5, ge=1, le=30)):
    """Get Predicto AI forecast for symbol"""
    try:
        forecast = await atlas_orchestrator.market_engine.predicto.get_forecast(symbol.upper(), days)

        if forecast:
            return {
                "success": True,
                "symbol": symbol.upper(),
                "forecast": forecast,
                "days": days,
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "success": False,
                "error": "Unable to get forecast",
                "symbol": symbol.upper()
            }

    except Exception as e:
        logger.error(f"Predicto forecast error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/predicto/sentiment")
async def get_predicto_market_sentiment():
    """Get Predicto market sentiment analysis"""
    try:
        sentiment = await atlas_orchestrator.market_engine.predicto.get_market_sentiment()

        return {
            "success": True,
            "sentiment": sentiment,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Predicto sentiment error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/predicto/enhanced-prediction/{symbol}")
async def get_enhanced_prediction(
    symbol: str,
    timeframe: str = Query("1day", regex="^(1min|5min|15min|30min|1hour|1day)$"),
    include_sentiment: bool = Query(True)
):
    """Get enhanced prediction combining Predicto AI with market analysis"""
    try:
        prediction = await atlas_orchestrator.market_engine.predicto.get_enhanced_prediction(
            symbol.upper(), timeframe, include_sentiment
        )

        return prediction

    except Exception as e:
        logger.error(f"Enhanced prediction error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/predicto/status")
async def get_predicto_status():
    """Get Predicto integration status"""
    try:
        predicto = atlas_orchestrator.market_engine.predicto

        return {
            "enabled": predicto.enabled,
            "api_key_configured": bool(predicto.api_key),
            "base_url": predicto.base_url,
            "status": "active" if predicto.enabled else "disabled"
        }

    except Exception as e:
        logger.error(f"Predicto status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/risk-assessment")
async def risk_assessment(request: AnalysisRequest):
    """Get risk assessment for symbol"""
    try:
        # Get quote first
        analysis = await atlas_orchestrator.market_engine.get_comprehensive_analysis(request.symbol)
        
        if "error" in analysis:
            raise HTTPException(status_code=500, detail=analysis["error"])
        
        quote = analysis["quote"]
        
        # Perform risk assessment
        risk_result = atlas_orchestrator.risk_engine.comprehensive_risk_assessment(
            symbol=request.symbol,
            entry_price=quote["price"],
            stop_loss=quote["price"] * 0.97,  # 3% stop loss
            account_size=atlas_orchestrator.user_profile["account_size"],
            confidence_score=0.7,
            current_positions=[],
            market_conditions={"vix": 20}
        )
        
        return risk_result
        
    except Exception as e:
        logger.error(f"Risk assessment error for {request.symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/education")
async def education_query(request: ChatRequest):
    """Educational query endpoint"""
    try:
        session_id = request.session_id or atlas_orchestrator.current_session_id
        
        education_result = atlas_orchestrator.education_engine.process_educational_query(
            query=request.message,
            session_id=session_id,
            user_level=atlas_orchestrator.user_profile["experience_level"]
        )
        
        return education_result
        
    except Exception as e:
        logger.error(f"Education query error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/learning-progress")
async def get_learning_progress(session_id: Optional[str] = None):
    """Get learning progress summary"""
    try:
        session_id = session_id or atlas_orchestrator.current_session_id or "default"
        
        learning_summary = atlas_orchestrator.education_engine.get_learning_summary(session_id)
        return learning_summary
        
    except Exception as e:
        logger.error(f"Learning progress error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/profile")
async def update_user_profile(profile: UserProfileUpdate):
    """Update user profile"""
    try:
        # Convert to dict and filter None values
        profile_updates = {k: v for k, v in profile.dict().items() if v is not None}
        
        # Update profile
        atlas_orchestrator.set_user_profile(profile_updates)
        
        return {
            "status": "success",
            "updated_profile": atlas_orchestrator.user_profile,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Profile update error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/system-status")
async def get_system_status():
    """Get comprehensive system status"""
    try:
        system_status = await atlas_orchestrator.get_system_status()
        return system_status
        
    except Exception as e:
        logger.error(f"System status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/profit-optimization")
async def profit_optimization():
    """Execute profit optimization strategy"""
    try:
        account_size = atlas_orchestrator.user_profile["account_size"]
        
        optimization_result = await atlas_orchestrator.trading_engine.execute_profit_optimization_strategy(account_size)
        
        return optimization_result

    except Exception as e:
        logger.error(f"Profit optimization error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/search-market-context")
async def search_market_context(request: Dict[str, Any]) -> Dict[str, Any]:
    """Search for market context using web search"""
    try:
        symbol = request.get('symbol', '').upper()
        query_type = request.get('query_type', 'news')

        if not symbol:
            return {
                "success": False,
                "error": "Symbol is required",
                "timestamp": datetime.utcnow()
            }

        logger.info(f"🔍 Searching market context for {symbol} ({query_type})...")

        # Use market engine's web search integration
        results = await atlas_orchestrator.market_engine.search_market_context(symbol, query_type)

        return results

    except Exception as e:
        logger.error(f"Market context search error: {e}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.utcnow()
        }

@app.get("/api/v1/web-search-status")
async def web_search_status() -> Dict[str, Any]:
    """Check web search integration status"""
    try:
        market_engine = atlas_orchestrator.market_engine

        # Check if web search is available
        web_search_available = hasattr(market_engine, 'web_search') and market_engine.web_search is not None

        if web_search_available:
            # Check which providers are configured
            providers = market_engine.web_search.providers if hasattr(market_engine.web_search, 'providers') else {}
            enabled_providers = [name for name, config in providers.items() if config.get('enabled', False)]

            return {
                "web_search_available": True,
                "enabled_providers": enabled_providers,
                "primary_provider": getattr(market_engine.web_search, 'primary_provider', 'unknown'),
                "status": "configured"
            }
        else:
            return {
                "web_search_available": False,
                "enabled_providers": [],
                "status": "not_configured",
                "message": "Web search requires Google, Bing, or DuckDuckGo API configuration"
            }

    except Exception as e:
        logger.error(f"Web search status error: {e}")
        return {
            "web_search_available": False,
            "error": str(e),
            "status": "error"
        }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {"error": "Endpoint not found", "detail": "Please check the API documentation"}

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return {"error": "Internal server error", "detail": "Please try again later"}

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 Starting Streamlined A.T.L.A.S AI Server...")
    print("🎯 Advanced Trading & Learning Analysis System")
    print("🧠 Consolidated Architecture with Unified Orchestrator")
    
    uvicorn.run(
        "atlas_server:app",
        host="0.0.0.0",
        port=settings.PORT,
        reload=True,
        log_level=settings.LOG_LEVEL.lower()
    )
